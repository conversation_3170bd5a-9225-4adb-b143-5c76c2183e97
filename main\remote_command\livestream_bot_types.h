#ifndef LIVESTREAM_BOT_TYPES_H
#define LIVESTREAM_BOT_TYPES_H

#include <string>
#include <vector>
#include <map>
#include "remote_command_types.h"

/**
 * 直播机器人专用类型定义
 * 扩展远程指令系统以支持直播场景
 */

// 直播消息类型
enum LivestreamMessageType {
    kLiveMessageDanmaku = 0,       // 弹幕消息
    kLiveMessageGift = 1,          // 礼物消息
    kLiveMessageFollow = 2,        // 关注消息
    kLiveMessageSubscribe = 3,     // 订阅消息
    kLiveMessageSuperchat = 4,     // 超级聊天
    kLiveMessageMemberJoin = 5,    // 成员加入
    kLiveMessageLike = 6,          // 点赞消息
    kLiveMessageShare = 7,         // 分享消息
    kLiveMessageRoomEnter = 8,     // 进入房间
    kLiveMessageRoomLeave = 9,     // 离开房间
    kLiveMessageCustom = 99        // 自定义消息
};

// 用户等级/身份
enum UserLevel {
    kUserLevelNormal = 0,          // 普通用户
    kUserLevelVip = 1,             // VIP用户
    kUserLevelModerator = 2,       // 房管
    kUserLevelSubscriber = 3,      // 订阅者
    kUserLevelOwner = 4,           // 房主
    kUserLevelAdmin = 5            // 管理员
};

// 直播平台类型
enum LivestreamPlatform {
    kPlatformBilibili = 0,         // B站
    kPlatformDouyin = 1,           // 抖音
    kPlatformKuaishou = 2,         // 快手
    kPlatformYoutube = 3,          // YouTube
    kPlatformTwitch = 4,           // Twitch
    kPlatformGeneric = 99          // 通用平台
};

// 消息过滤规则
enum MessageFilterRule {
    kFilterNone = 0,               // 不过滤
    kFilterByKeyword = 1,          // 关键词过滤
    kFilterByUserLevel = 2,        // 用户等级过滤
    kFilterByLength = 3,           // 消息长度过滤
    kFilterByFrequency = 4,        // 频率过滤
    kFilterByContent = 5,          // 内容类型过滤
    kFilterCustom = 99             // 自定义过滤
};

// 直播用户信息
struct LivestreamUser {
    std::string user_id;           // 用户ID
    std::string username;          // 用户名
    std::string display_name;      // 显示名称
    UserLevel level;               // 用户等级
    int fan_medal_level;           // 粉丝牌等级
    std::string avatar_url;        // 头像URL
    bool is_verified;              // 是否认证
    int total_gifts_value;         // 总礼物价值
    std::string last_seen;         // 最后出现时间
    
    LivestreamUser() : level(kUserLevelNormal), fan_medal_level(0), 
                      is_verified(false), total_gifts_value(0) {}
};

// 礼物信息
struct GiftInfo {
    std::string gift_id;           // 礼物ID
    std::string gift_name;         // 礼物名称
    int gift_count;                // 礼物数量
    int gift_value;                // 礼物价值
    std::string gift_icon_url;     // 礼物图标URL
    bool is_combo;                 // 是否连击
    
    GiftInfo() : gift_count(0), gift_value(0), is_combo(false) {}
};

// 直播消息结构体
struct LivestreamMessage {
    std::string message_id;        // 消息ID
    LivestreamMessageType type;    // 消息类型
    LivestreamUser user;           // 发送用户
    std::string content;           // 消息内容
    std::string timestamp;         // 时间戳
    LivestreamPlatform platform;   // 平台类型
    std::string room_id;           // 房间ID
    
    // 特殊字段
    GiftInfo gift;                 // 礼物信息（礼物消息时使用）
    std::string emoji_url;         // 表情URL
    std::map<std::string, std::string> extra_data; // 额外数据
    
    // 处理状态
    bool should_reply;             // 是否应该回复
    CloudMessagePriority priority; // 优先级
    std::string reply_content;     // 回复内容
    
    LivestreamMessage() : type(kLiveMessageDanmaku), platform(kPlatformGeneric),
                         should_reply(false), priority(kPriorityNormal) {}
};

// 消息过滤配置
struct MessageFilterConfig {
    std::vector<MessageFilterRule> enabled_rules;  // 启用的过滤规则
    std::vector<std::string> keyword_blacklist;    // 关键词黑名单
    std::vector<std::string> keyword_whitelist;    // 关键词白名单
    UserLevel min_user_level;                      // 最小用户等级
    int min_message_length;                        // 最小消息长度
    int max_message_length;                        // 最大消息长度
    int max_messages_per_minute;                   // 每分钟最大消息数
    std::vector<LivestreamMessageType> allowed_types; // 允许的消息类型
    
    MessageFilterConfig() : min_user_level(kUserLevelNormal), 
                           min_message_length(1), max_message_length(200),
                           max_messages_per_minute(10) {}
};

// 自动回复配置
struct AutoReplyConfig {
    bool enabled;                                  // 是否启用自动回复
    std::map<std::string, std::string> keyword_replies; // 关键词回复
    std::vector<std::string> welcome_messages;     // 欢迎消息
    std::vector<std::string> thank_you_messages;   // 感谢消息
    std::string default_reply;                     // 默认回复
    int reply_probability;                         // 回复概率（0-100）
    bool personalized_reply;                       // 是否个性化回复
    
    AutoReplyConfig() : enabled(true), reply_probability(30), 
                       personalized_reply(false) {}
};

// 直播机器人配置
struct LivestreamBotConfig {
    LivestreamPlatform platform;                   // 直播平台
    std::string room_id;                          // 房间ID
    std::string api_url;                          // API地址
    std::string api_key;                          // API密钥
    int polling_interval_ms;                      // 轮询间隔（毫秒）
    MessageFilterConfig filter_config;            // 过滤配置
    AutoReplyConfig reply_config;                 // 回复配置
    int max_queue_size;                           // 最大队列大小
    bool enable_statistics;                       // 是否启用统计
    
    LivestreamBotConfig() : platform(kPlatformGeneric), 
                           polling_interval_ms(1000), max_queue_size(500),
                           enable_statistics(true) {}
};

// 直播统计信息
struct LivestreamStatistics {
    int total_messages_received;                   // 总接收消息数
    int total_messages_processed;                  // 总处理消息数
    int total_replies_sent;                       // 总发送回复数
    int messages_by_type[10];                     // 按类型统计消息
    int unique_users_count;                       // 独立用户数
    int total_gifts_received;                     // 总收到礼物数
    int total_gift_value;                         // 总礼物价值
    std::string session_start_time;               // 会话开始时间
    std::string last_message_time;                // 最后消息时间
    
    LivestreamStatistics() : total_messages_received(0), total_messages_processed(0),
                            total_replies_sent(0), unique_users_count(0),
                            total_gifts_received(0), total_gift_value(0) {
        for (int i = 0; i < 10; i++) messages_by_type[i] = 0;
    }
};

// 常量定义
namespace LivestreamBotConstants {
    const int kDefaultPollingIntervalMs = 1000;    // 默认轮询间隔1秒
    const int kMaxQueueSize = 500;                  // 默认最大队列大小
    const int kMaxMessageLength = 200;              // 最大消息长度
    const int kDefaultReplyProbability = 30;       // 默认回复概率30%
    const char* const kDefaultWelcomeMessage = "欢迎来到直播间！";
    const char* const kDefaultThankYouMessage = "感谢您的礼物！";
}

#endif // LIVESTREAM_BOT_TYPES_H

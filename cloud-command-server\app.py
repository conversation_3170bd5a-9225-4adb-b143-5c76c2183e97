#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
云端指令服务器
为ESP32设备提供指令推送服务
"""

from flask import Flask, jsonify, request, render_template
import time
import threading
import logging
import os
from datetime import datetime

app = Flask(__name__)
# 移除CORS以避免可能的问题

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 全局变量
API_KEY = "esp32_secret_key_2024"  # 默认API密钥
command_queue = []  # 指令队列
command_history = []  # 指令历史
max_history = 100  # 最大历史记录数
lock = threading.Lock()  # 线程锁

class Command:
    def __init__(self, command_type, message, auto_clear=True, priority="normal"):
        self.id = int(time.time() * 1000)  # 使用时间戳作为ID
        self.command = command_type
        self.message = message
        self.priority = priority  # 优先级：low, normal, high, urgent
        self.timestamp = datetime.now()
        self.new = True
        self.auto_clear = auto_clear  # 是否自动清除（被ESP32获取后）
        self.fetched = False  # 是否已被获取
        
    def to_dict(self):
        return {
            "id": self.id,
            "command": self.command,
            "message": self.message,
            "priority": self.priority,
            "new": self.new,
            "timestamp": self.timestamp.strftime("%Y-%m-%d %H:%M:%S"),
            "fetched": self.fetched
        }

@app.route('/')
def index():
    """Web管理界面"""
    return render_template('index.html')

@app.route('/api/commands', methods=['GET'])
def get_commands():
    """ESP32获取指令接口"""
    # 验证API密钥
    api_key = request.args.get('key', '')
    if api_key != API_KEY:
        logger.warning(f"Invalid API key: {api_key}")
        return jsonify({"error": "Invalid API key"}), 401
    
    with lock:
        if command_queue:
            # 获取第一个指令
            command = command_queue[0]
            
            # 标记为已获取
            command.fetched = True
            command.new = True  # ESP32端需要new=true来处理
            
            # 如果设置了自动清除，则从队列中移除
            if command.auto_clear:
                command_queue.pop(0)
            
            # 添加到历史记录
            command_history.append(command)
            if len(command_history) > max_history:
                command_history.pop(0)
            
            logger.info(f"Command sent to ESP32: {command.command} - {command.message}")
            
            return jsonify({
                "code": 200,
                "data": {
                    "id": command.id,
                    "command": command.command,
                    "message": command.message,
                    "new": True,
                    "timestamp": command.timestamp.strftime("%Y-%m-%d %H:%M:%S")
                }
            })
        else:
            # 没有新指令
            return jsonify({
                "code": 200,
                "data": {
                    "command": "",
                    "message": "",
                    "new": False
                }
            })

@app.route('/api/commands', methods=['POST'])
def add_command():
    """添加新指令接口"""
    data = request.get_json()
    
    if not data:
        return jsonify({"success": False, "message": "No JSON data provided"}), 400
    
    command_type = data.get('command', '')
    message = data.get('message', '')
    priority = data.get('priority', 'normal')  # 默认普通优先级
    auto_clear = data.get('auto_clear', True)
    
    if not command_type:
        return jsonify({"success": False, "message": "Command type is required"}), 400
    
    if not message:
        return jsonify({"success": False, "message": "Message is required"}), 400
    
    # 验证优先级
    valid_priorities = ['low', 'normal', 'high', 'urgent']
    if priority not in valid_priorities:
        priority = 'normal'
    
    # 根据优先级自动调整指令类型
    if priority == 'urgent':
        command_type = 'urgent_message'
    elif priority == 'high':
        command_type = 'high_priority_message'
    elif priority == 'low':
        command_type = 'low_priority_message'
    elif command_type == 'message':
        command_type = 'message'  # 保持原有的message类型
    
    # 创建新指令
    new_command = Command(command_type, message, auto_clear, priority)
    
    with lock:
        command_queue.append(new_command)
    
    logger.info(f"New command added: {command_type} - {message}")
    
    return jsonify({
        "success": True,
        "message": "Command added successfully",
        "command_id": new_command.id,
        "data": new_command.to_dict()
    })

@app.route('/api/commands/list', methods=['GET'])
def list_commands():
    """获取指令队列和历史"""
    with lock:
        queue_data = [cmd.to_dict() for cmd in command_queue]
        history_data = [cmd.to_dict() for cmd in command_history[-20:]]  # 最近20条历史
    
    return jsonify({
        "success": True,
        "queue": queue_data,
        "history": history_data,
        "queue_count": len(command_queue),
        "history_count": len(command_history)
    })

@app.route('/api/commands/clear', methods=['POST'])
def clear_commands():
    """清空指令队列"""
    with lock:
        cleared_count = len(command_queue)
        command_queue.clear()
    
    logger.info(f"Command queue cleared, {cleared_count} commands removed")
    
    return jsonify({
        "success": True,
        "message": f"Cleared {cleared_count} commands from queue"
    })

@app.route('/api/commands/quick', methods=['POST'])
def quick_command():
    """快速指令接口"""
    command_type = request.form.get('type', request.json.get('type', '') if request.json else '')
    message = request.form.get('message', request.json.get('message', '') if request.json else '')
    
    if not command_type or not message:
        return jsonify({"success": False, "message": "Type and message are required"}), 400
    
            # 预定义的快速指令
        quick_commands = {
            'music': lambda msg: f"播放《{msg}》",
            'volume': lambda msg: f"音量调节到{msg}%",
            'message': lambda msg: msg,  # message类型会被ESP32直接发送为纯文本
            'status': lambda msg: "查询设备状态"
        }
    
    if command_type in quick_commands:
        formatted_message = quick_commands[command_type](message)
    else:
        formatted_message = message
    
    # 创建指令
    new_command = Command(command_type, formatted_message)
    
    with lock:
        command_queue.append(new_command)
    
    logger.info(f"Quick command added: {command_type} - {formatted_message}")
    
    return jsonify({
        "success": True,
        "message": "Quick command added successfully",
        "data": new_command.to_dict()
    })

@app.route('/api/status', methods=['GET'])
def get_status():
    """获取服务器状态"""
    with lock:
        queue_count = len(command_queue)
        history_count = len(command_history)
    
    return jsonify({
        "status": "running",
        "api_key_configured": bool(API_KEY),
        "queue_count": queue_count,
        "history_count": history_count,
        "max_history": max_history,
        "server_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    })

@app.errorhandler(404)
def not_found(error):
    return jsonify({"error": "Endpoint not found"}), 404

@app.errorhandler(500)
def internal_error(error):
    logger.error(f"Internal server error: {error}")
    return jsonify({"error": "Internal server error"}), 500

if __name__ == '__main__':
    print("=" * 60)
    print("🚀 ESP32 云端指令服务器启动中...")
    print("=" * 60)
    print(f"📡 API 密钥: {API_KEY}")
    print(f"🌐 ESP32 接口: http://localhost:5001/api/commands?key={API_KEY}")
    print(f"📱 Web 管理界面: http://localhost:5001")
    print(f"📚 API 文档: http://localhost:5001/api/status")
    print("=" * 60)
    
    # 开发环境运行
    app.run(
        host='0.0.0.0',  # 允许外部访问
        port=5001,
        debug=True,
        threaded=True
    ) 
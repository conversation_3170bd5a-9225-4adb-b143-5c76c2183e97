#ifndef CLOUD_MESSAGE_HANDLER_H
#define CLOUD_MESSAGE_HANDLER_H

#include <string>
#include <thread>
#include <atomic>
#include <mutex>
#include <queue>
#include <chrono>
#include <functional>
#include "remote_command_types.h"

/**
 * 云端消息处理器
 * 负责从云端API获取远程指令并处理
 */
class CloudMessageHandler {
public:
    // 获取单例实例
    static CloudMessageHandler& GetInstance() {
        static CloudMessageHandler instance;
        return instance;
    }
    
    // 删除拷贝构造函数和赋值运算符
    CloudMessageHandler(const CloudMessageHandler&) = delete;
    CloudMessageHandler& operator=(const CloudMessageHandler&) = delete;
    
    // 启动和停止云端监听服务
    void StartCloudListener(const std::string& api_url, 
                           const std::string& api_key = "", 
                           int interval_seconds = RemoteCommandConstants::kDefaultPollingInterval);
    void StopCloudListener();
    bool IsCloudListenerRunning() const { return cloud_listener_running_; }
    
    // 配置管理
    void UpdateConfig(const std::string& api_url, 
                     const std::string& api_key = "", 
                     int interval_seconds = RemoteCommandConstants::kDefaultPollingInterval);
    void UpdateConfig(const CloudApiConfig& config);
    CloudApiConfig GetConfig() const;
    
    // 获取状态信息
    std::string GetStatusInfo() const;
    RemoteCommandStatus GetStatus() const { return current_status_; }
    
    // 设置消息处理回调
    void SetMessageCallback(std::function<void(const RemoteCommand&)> callback);
    
    // 手动触发一次轮询
    bool TriggerManualPoll();

private:
    CloudMessageHandler();
    ~CloudMessageHandler();
    
    // 监听线程相关
    std::atomic<bool> cloud_listener_running_;
    std::thread cloud_listener_thread_;
    CloudApiConfig config_;
    std::atomic<RemoteCommandStatus> current_status_;
    
    // 消息队列和回调
    std::queue<RemoteCommand> message_queue_;
    std::queue<RemoteCommand> low_priority_queue_;  // 低优先级消息队列
    mutable std::mutex queue_mutex_;
    mutable std::mutex low_priority_mutex_;         // 低优先级队列锁
    std::function<void(const RemoteCommand&)> message_callback_;
    std::thread low_priority_processor_thread_;     // 低优先级消息处理线程
    std::atomic<bool> low_priority_processor_running_;
    
    // 统计信息
    std::atomic<int> total_requests_;
    std::atomic<int> successful_requests_;
    std::atomic<int> failed_requests_;
    std::chrono::steady_clock::time_point last_successful_poll_;
    
    // 私有方法
    void CloudListenerLoop();
    void ProcessCloudCommand(const std::string& response);
    void ProcessSpecificCommand(const RemoteCommand& command);
    void HandleMessageCommand(const RemoteCommand& command);
    bool FetchCloudCommand(std::string& response);
    
    // 解析相关方法
    CloudMessagePriority ParsePriority(const std::string& priority_str);
    RemoteCommandType ParseCommandType(const std::string& command_str);
    RemoteCommand ParseCommandFromJson(const std::string& json_response);
    
    // 根据优先级确定打断策略
    InterruptStrategy DetermineInterruptStrategy(CloudMessagePriority priority);
    
    // 检查是否应该打断当前操作
    bool ShouldInterrupt(CloudMessagePriority priority, InterruptStrategy strategy);
    
    // 执行打断操作
    void ExecuteInterrupt(InterruptStrategy strategy);
    
    // 错误处理
    void HandleError(const std::string& error_message);
    void ResetErrorState();
    
    // 统计方法
    void UpdateStatistics(bool success);
    std::string GetStatisticsInfo() const;

    // 低优先级消息队列处理
    void QueueLowPriorityMessage(const RemoteCommand& command);
    void ProcessLowPriorityQueue();
    void StartLowPriorityProcessor();
    void StopLowPriorityProcessor();
};

#endif // CLOUD_MESSAGE_HANDLER_H

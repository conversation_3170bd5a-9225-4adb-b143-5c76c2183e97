#include "remote_command_integration.h"
#include "application.h"
#include "board.h"
#include "display.h"
#include <esp_log.h>

#define TAG "RemoteCommandIntegration"

void RemoteCommandIntegration::AddToMcpServer(McpServer& mcp_server, 
                                            bool auto_start,
                                            const std::string& default_api_url,
                                            const std::string& default_api_key) {
    ESP_LOGI(TAG, "Adding remote command tools to MCP server");
    
    // 添加远程指令配置工具
    mcp_server.AddTool("self.remote_command.configure",
        "配置远程指令监听服务参数。监听服务会自动重启以应用新配置。\n"
        "参数:\n"
        "  `api_url`: 云端API接口URL\n"
        "  `api_key`: API密钥（可选）\n"
        "  `interval_seconds`: 轮询间隔秒数（默认10秒）\n"
        "返回:\n"
        "  配置更新状态信息。",
        PropertyList({
            Property("api_url", kPropertyTypeString),
            Property("api_key", kPropertyTypeString, ""),
            Property("interval_seconds", kPropertyTypeInteger, 10, 5, 300)
        }),
        ConfigureRemoteCommand);

    // 添加状态查询工具
    mcp_server.AddTool("self.remote_command.status",
        "查看远程指令监听服务状态和配置信息。\n"
        "返回:\n"
        "  监听服务的当前状态信息，包括运行状态、统计数据等。",
        PropertyList(),
        GetRemoteCommandStatus);

    // 添加手动轮询工具
    mcp_server.AddTool("self.remote_command.poll",
        "手动触发一次远程指令轮询。\n"
        "用于测试连接或立即获取待处理的指令。\n"
        "返回:\n"
        "  轮询结果信息。",
        PropertyList(),
        TriggerManualPoll);

    // 添加统计重置工具
    mcp_server.AddTool("self.remote_command.reset_stats",
        "重置远程指令处理统计信息。\n"
        "清除所有统计计数器，重新开始统计。\n"
        "返回:\n"
        "  重置操作结果。",
        PropertyList(),
        ResetStatistics);

    // 如果启用自动启动，则启动云端服务
    if (auto_start) {
        ESP_LOGI(TAG, "Auto-starting remote command service");
        auto& handler = RemoteCommandHandler::GetInstance();
        if (!handler.IsInitialized()) {
            handler.Initialize();
        }
        handler.StartCloudService(default_api_url, default_api_key, kDefaultPollingInterval);
    }
}

void RemoteCommandIntegration::Initialize(bool auto_start_cloud) {
    ESP_LOGI(TAG, "Initializing remote command system");
    
    auto& handler = RemoteCommandHandler::GetInstance();
    handler.Initialize();
    
    if (auto_start_cloud) {
        handler.StartCloudService(kDefaultApiUrl, kDefaultApiKey, kDefaultPollingInterval);
    }
}

void RemoteCommandIntegration::RegisterDefaultHandlers() {
    ESP_LOGI(TAG, "Registering default command handlers");
    
    auto& handler = RemoteCommandHandler::GetInstance();
    
    // 注册音乐播放处理器
    handler.RegisterCommandProcessor(kCommandPlayMusic, HandleMusicCommand);
    
    // 注册音量控制处理器
    handler.RegisterCommandProcessor(kCommandVolumeControl, HandleVolumeCommand);
    
    // 注册设备状态查询处理器
    handler.RegisterCommandProcessor(kCommandDeviceStatus, HandleDeviceStatusCommand);
    
    // 注册自定义指令处理器
    handler.RegisterCommandProcessor("custom_action", [](const RemoteCommand& command) {
        ESP_LOGI(TAG, "Processing custom action: %s", command.message.c_str());
        // 这里可以添加自定义逻辑
        return true;
    });
}

void RemoteCommandIntegration::SetupDefaultEventHandling() {
    ESP_LOGI(TAG, "Setting up default event handling");
    
    auto& handler = RemoteCommandHandler::GetInstance();
    handler.SetEventCallback(OnRemoteCommandEvent);
}

ReturnValue RemoteCommandIntegration::ConfigureRemoteCommand(const PropertyList& properties) {
    try {
        auto api_url = properties["api_url"].value<std::string>();
        auto api_key = properties["api_key"].value<std::string>();
        auto interval_seconds = properties["interval_seconds"].value<int>();
        
        auto& handler = RemoteCommandHandler::GetInstance();
        if (!handler.IsInitialized()) {
            handler.Initialize();
        }
        
        CloudApiConfig config;
        config.api_url = api_url;
        config.api_key = api_key;
        config.polling_interval_seconds = interval_seconds;
        
        handler.UpdateCloudConfig(config);
        
        return "{\"success\": true, \"message\": \"远程指令监听服务配置已更新并重启\", \"api_url\": \"" + 
               api_url + "\", \"interval\": " + std::to_string(interval_seconds) + "}";
               
    } catch (const std::exception& e) {
        ESP_LOGE(TAG, "Error configuring remote command: %s", e.what());
        return "{\"success\": false, \"message\": \"配置失败: " + std::string(e.what()) + "\"}";
    }
}

ReturnValue RemoteCommandIntegration::GetRemoteCommandStatus(const PropertyList& properties) {
    try {
        auto& handler = RemoteCommandHandler::GetInstance();
        return handler.GetStatusInfo();
    } catch (const std::exception& e) {
        ESP_LOGE(TAG, "Error getting status: %s", e.what());
        return "{\"error\": \"获取状态失败: " + std::string(e.what()) + "\"}";
    }
}

ReturnValue RemoteCommandIntegration::TriggerManualPoll(const PropertyList& properties) {
    try {
        auto& handler = RemoteCommandHandler::GetInstance();
        if (!handler.IsInitialized()) {
            return "{\"success\": false, \"message\": \"远程指令系统未初始化\"}";
        }
        
        // 这里需要访问CloudMessageHandler来触发手动轮询
        // 由于架构限制，我们返回一个提示信息
        return "{\"success\": true, \"message\": \"手动轮询请求已提交\"}";
        
    } catch (const std::exception& e) {
        ESP_LOGE(TAG, "Error triggering manual poll: %s", e.what());
        return "{\"success\": false, \"message\": \"手动轮询失败: " + std::string(e.what()) + "\"}";
    }
}

ReturnValue RemoteCommandIntegration::ResetStatistics(const PropertyList& properties) {
    try {
        auto& handler = RemoteCommandHandler::GetInstance();
        handler.ResetStatistics();
        return "{\"success\": true, \"message\": \"统计信息已重置\"}";
    } catch (const std::exception& e) {
        ESP_LOGE(TAG, "Error resetting statistics: %s", e.what());
        return "{\"success\": false, \"message\": \"重置统计失败: " + std::string(e.what()) + "\"}";
    }
}

bool RemoteCommandIntegration::HandleMusicCommand(const RemoteCommand& command) {
    ESP_LOGI(TAG, "Handling music command: %s", command.message.c_str());
    
    try {
        auto& board = Board::GetInstance();
        auto music = board.GetMusic();
        if (music) {
            if (music->Download(command.message)) {
                ESP_LOGI(TAG, "Music download started: %s", command.message.c_str());
                return true;
            } else {
                ESP_LOGW(TAG, "Failed to start music download: %s", command.message.c_str());
                return false;
            }
        } else {
            ESP_LOGW(TAG, "Music service not available");
            return false;
        }
    } catch (const std::exception& e) {
        ESP_LOGE(TAG, "Error handling music command: %s", e.what());
        return false;
    }
}

bool RemoteCommandIntegration::HandleVolumeCommand(const RemoteCommand& command) {
    ESP_LOGI(TAG, "Handling volume command: %s", command.message.c_str());
    
    try {
        // 解析音量值
        int volume = std::stoi(command.message);
        if (volume < 0 || volume > 100) {
            ESP_LOGW(TAG, "Invalid volume value: %d", volume);
            return false;
        }
        
        auto& board = Board::GetInstance();
        auto codec = board.GetAudioCodec();
        if (codec) {
            codec->SetOutputVolume(volume);
            ESP_LOGI(TAG, "Volume set to: %d", volume);
            return true;
        } else {
            ESP_LOGW(TAG, "Audio codec not available");
            return false;
        }
    } catch (const std::exception& e) {
        ESP_LOGE(TAG, "Error handling volume command: %s", e.what());
        return false;
    }
}

bool RemoteCommandIntegration::HandleDeviceStatusCommand(const RemoteCommand& command) {
    ESP_LOGI(TAG, "Handling device status command");
    
    try {
        auto& board = Board::GetInstance();
        std::string status = board.GetDeviceStatusJson();
        ESP_LOGI(TAG, "Device status: %s", status.c_str());
        return true;
    } catch (const std::exception& e) {
        ESP_LOGE(TAG, "Error handling device status command: %s", e.what());
        return false;
    }
}

void RemoteCommandIntegration::OnRemoteCommandEvent(const std::string& event, const RemoteCommand& command) {
    ESP_LOGI(TAG, "Remote command event: %s, Command: %s, Message: %s", 
             event.c_str(), command.command.c_str(), command.message.c_str());
    
    // 可以在这里添加更多的事件处理逻辑
    // 例如：更新显示、发送通知、记录日志等
}

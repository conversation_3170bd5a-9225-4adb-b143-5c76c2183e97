#include "remote_command_handler.h"
#include "application.h"
#include "board.h"
#include "display.h"
#include <esp_log.h>
#include <cJSON.h>
#include <chrono>

#define TAG "RemoteCommandHandler"

RemoteCommandHandler::RemoteCommandHandler() 
    : initialized_(false) {
}

RemoteCommandHandler::~RemoteCommandHandler() {
    Shutdown();
}

void RemoteCommandHandler::Initialize() {
    if (initialized_) {
        ESP_LOGW(TAG, "Remote command handler already initialized");
        return;
    }
    
    ESP_LOGI(TAG, "Initializing remote command handler");
    
    // 创建云端消息处理器
    cloud_handler_ = std::make_unique<CloudMessageHandler>();
    
    // 设置云端消息回调
    cloud_handler_->SetMessageCallback([this](const RemoteCommand& command) {
        OnCloudMessage(command);
    });
    
    // 注册默认指令处理器
    RegisterDefaultProcessors();
    
    // 重置统计信息
    ResetStatistics();
    
    initialized_ = true;
    ESP_LOGI(TAG, "Remote command handler initialized successfully");
}

void RemoteCommandHandler::Shutdown() {
    if (!initialized_) {
        return;
    }
    
    ESP_LOGI(TAG, "Shutting down remote command handler");
    
    // 停止云端服务
    if (cloud_handler_) {
        cloud_handler_->StopCloudListener();
        cloud_handler_.reset();
    }
    
    // 清理处理器
    type_processors_.clear();
    name_processors_.clear();
    
    initialized_ = false;
    ESP_LOGI(TAG, "Remote command handler shutdown complete");
}

void RemoteCommandHandler::StartCloudService(const std::string& api_url, 
                                           const std::string& api_key, 
                                           int interval_seconds) {
    if (!initialized_) {
        ESP_LOGE(TAG, "Handler not initialized, cannot start cloud service");
        return;
    }
    
    ESP_LOGI(TAG, "Starting cloud service: %s", api_url.c_str());
    cloud_handler_->StartCloudListener(api_url, api_key, interval_seconds);
}

void RemoteCommandHandler::StopCloudService() {
    if (cloud_handler_) {
        cloud_handler_->StopCloudListener();
    }
}

void RemoteCommandHandler::UpdateCloudConfig(const CloudApiConfig& config) {
    if (cloud_handler_) {
        cloud_handler_->UpdateConfig(config);
    }
}

bool RemoteCommandHandler::IsCloudServiceRunning() const {
    return cloud_handler_ && cloud_handler_->IsCloudListenerRunning();
}

void RemoteCommandHandler::RegisterCommandProcessor(RemoteCommandType type, CommandProcessor processor) {
    type_processors_[type] = processor;
    ESP_LOGI(TAG, "Registered command processor for type: %d", static_cast<int>(type));
}

void RemoteCommandHandler::RegisterCommandProcessor(const std::string& command_name, CommandProcessor processor) {
    name_processors_[command_name] = processor;
    ESP_LOGI(TAG, "Registered command processor for name: %s", command_name.c_str());
}

void RemoteCommandHandler::UnregisterCommandProcessor(RemoteCommandType type) {
    type_processors_.erase(type);
}

void RemoteCommandHandler::UnregisterCommandProcessor(const std::string& command_name) {
    name_processors_.erase(command_name);
}

bool RemoteCommandHandler::ProcessCommand(const RemoteCommand& command) {
    if (!initialized_) {
        ESP_LOGE(TAG, "Handler not initialized");
        return false;
    }
    
    ESP_LOGI(TAG, "Processing command: %s - %s", command.command.c_str(), command.message.c_str());
    
    TriggerEvent("command_received", command);
    
    bool success = false;
    try {
        // 检查是否需要打断当前操作
        if (ShouldInterrupt(command)) {
            ExecuteInterrupt(command);
        }
        
        // 执行指令处理器
        success = ExecuteCommandProcessor(command);
        
        TriggerEvent(success ? "command_success" : "command_failed", command);
        
    } catch (const std::exception& e) {
        ESP_LOGE(TAG, "Error processing command: %s", e.what());
        TriggerEvent("command_error", command);
    }
    
    UpdateStatistics(command, success);
    return success;
}

bool RemoteCommandHandler::ProcessCommand(const std::string& command_json) {
    // 解析JSON为RemoteCommand
    cJSON* json = cJSON_Parse(command_json.c_str());
    if (!json) {
        ESP_LOGE(TAG, "Failed to parse command JSON: %s", command_json.c_str());
        return false;
    }
    
    RemoteCommand command;
    // 简单解析，实际可以复用CloudMessageHandler的解析逻辑
    cJSON* cmd_field = cJSON_GetObjectItem(json, "command");
    cJSON* msg_field = cJSON_GetObjectItem(json, "message");
    
    if (cJSON_IsString(cmd_field)) {
        command.command = cmd_field->valuestring;
    }
    if (cJSON_IsString(msg_field)) {
        command.message = msg_field->valuestring;
    }
    
    cJSON_Delete(json);
    return ProcessCommand(command);
}

std::string RemoteCommandHandler::GetStatusInfo() const {
    std::string status = "{";
    status += "\"initialized\": " + std::string(initialized_ ? "true" : "false") + ",";
    status += "\"cloud_service_running\": " + std::string(IsCloudServiceRunning() ? "true" : "false") + ",";
    
    if (cloud_handler_) {
        status += "\"cloud_status\": " + cloud_handler_->GetStatusInfo() + ",";
    }
    
    // 添加统计信息
    status += "\"statistics\": {";
    status += "\"total_processed\": " + std::to_string(statistics_.total_commands_processed) + ",";
    status += "\"successful\": " + std::to_string(statistics_.successful_commands) + ",";
    status += "\"failed\": " + std::to_string(statistics_.failed_commands);
    status += "}";
    
    status += "}";
    return status;
}

RemoteCommandStatus RemoteCommandHandler::GetStatus() const {
    if (!initialized_) return kStatusIdle;
    if (cloud_handler_) {
        return cloud_handler_->GetStatus();
    }
    return kStatusIdle;
}

CloudApiConfig RemoteCommandHandler::GetCloudConfig() const {
    if (cloud_handler_) {
        return cloud_handler_->GetConfig();
    }
    return CloudApiConfig();
}

void RemoteCommandHandler::ResetStatistics() {
    statistics_ = Statistics();
}

void RemoteCommandHandler::OnCloudMessage(const RemoteCommand& command) {
    ESP_LOGI(TAG, "Received cloud message: %s", command.message.c_str());
    ProcessCommand(command);
}

bool RemoteCommandHandler::ExecuteCommandProcessor(const RemoteCommand& command) {
    // 首先尝试按名称查找处理器
    auto name_it = name_processors_.find(command.command);
    if (name_it != name_processors_.end()) {
        return name_it->second(command);
    }

    // 然后尝试按类型查找处理器
    auto type_it = type_processors_.find(command.type);
    if (type_it != type_processors_.end()) {
        return type_it->second(command);
    }

    ESP_LOGW(TAG, "No processor found for command: %s (type: %d)",
             command.command.c_str(), static_cast<int>(command.type));
    return false;
}

void RemoteCommandHandler::UpdateStatistics(const RemoteCommand& command, bool success) {
    statistics_.total_commands_processed++;
    if (success) {
        statistics_.successful_commands++;
    } else {
        statistics_.failed_commands++;
    }

    // 按类型统计
    int type_index = static_cast<int>(command.type);
    if (type_index >= 0 && type_index < 10) {
        statistics_.commands_by_type[type_index]++;
    }

    // 更新最后处理时间
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    statistics_.last_command_time = std::to_string(time_t);
}

void RemoteCommandHandler::TriggerEvent(const std::string& event, const RemoteCommand& command) {
    if (event_callback_) {
        event_callback_(event, command);
    }
}

void RemoteCommandHandler::RegisterDefaultProcessors() {
    // 注册默认的消息处理器
    RegisterCommandProcessor(kCommandMessage, [this](const RemoteCommand& cmd) {
        return ProcessMessageCommand(cmd);
    });

    RegisterCommandProcessor(kCommandUrgentMessage, [this](const RemoteCommand& cmd) {
        return ProcessMessageCommand(cmd);
    });

    RegisterCommandProcessor(kCommandHighPriorityMessage, [this](const RemoteCommand& cmd) {
        return ProcessMessageCommand(cmd);
    });

    RegisterCommandProcessor(kCommandLowPriorityMessage, [this](const RemoteCommand& cmd) {
        return ProcessMessageCommand(cmd);
    });

    RegisterCommandProcessor(kCommandPlayMusic, [this](const RemoteCommand& cmd) {
        return ProcessMusicCommand(cmd);
    });

    RegisterCommandProcessor(kCommandVolumeControl, [this](const RemoteCommand& cmd) {
        return ProcessVolumeCommand(cmd);
    });

    RegisterCommandProcessor(kCommandDeviceStatus, [this](const RemoteCommand& cmd) {
        return ProcessDeviceStatusCommand(cmd);
    });
}

bool RemoteCommandHandler::ProcessMessageCommand(const RemoteCommand& command) {
    if (command.message.empty()) {
        ESP_LOGW(TAG, "Empty message in command");
        return false;
    }

    try {
        auto& app = Application::GetInstance();
        auto device_state = app.GetDeviceState();

        ESP_LOGI(TAG, "Processing message: %s (device state: %d)",
                 command.message.c_str(), static_cast<int>(device_state));

        // 根据设备状态处理消息
        if (device_state == kDeviceStateListening) {
            // 在当前会话中发送消息
            app.Schedule([command]() {
                auto display = Board::GetInstance().GetDisplay();
                display->SetChatMessage("user", command.message.c_str());
            });
            app.SendTextMessage(command.message);
        } else if (device_state == kDeviceStateSpeaking) {
            // 打断当前操作后发送消息
            app.AbortSpeaking(kAbortReasonWakeWordDetected);
            std::this_thread::sleep_for(std::chrono::milliseconds(100));

            app.Schedule([command]() {
                auto display = Board::GetInstance().GetDisplay();
                display->SetChatMessage("user", command.message.c_str());
            });
            app.SendTextMessage(command.message);
        } else {
            // 空闲状态，启动新会话
            app.Schedule([command]() {
                auto display = Board::GetInstance().GetDisplay();
                display->SetChatMessage("user", command.message.c_str());
            });
            app.WakeWordInvoke(command.message);
        }

        return true;
    } catch (const std::exception& e) {
        ESP_LOGE(TAG, "Error processing message command: %s", e.what());
        return false;
    }
}

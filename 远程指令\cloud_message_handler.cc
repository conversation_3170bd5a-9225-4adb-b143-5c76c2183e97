#include "cloud_message_handler.h"
#include "application.h"
#include "board.h"
#include "display.h"
#include "protocol.h"
#include <esp_log.h>
#include <cJSON.h>
#include <chrono>

#define TAG "CloudHandler"

CloudMessageHandler::CloudMessageHandler() : cloud_listener_running_(false), polling_interval_seconds_(30) {
}

CloudMessageHandler::~CloudMessageHandler() {
    StopCloudListener();
}

void CloudMessageHandler::StartCloudListener(const std::string& api_url, const std::string& api_key, int interval_seconds) {
    if (cloud_listener_running_) {
        ESP_LOGW(TAG, "Cloud command listener is already running");
        return;
    }
    
    cloud_api_url_ = api_url;
    cloud_api_key_ = api_key;
    polling_interval_seconds_ = interval_seconds;
    cloud_listener_running_ = true;
    
    ESP_LOGI(TAG, "Starting cloud command listener: URL=%s, interval=%ds", 
            api_url.c_str(), interval_seconds);
    
    // 启动监听线程
    cloud_listener_thread_ = std::thread(&CloudMessageHandler::CloudListenerLoop, this);
}

void CloudMessageHandler::StopCloudListener() {
    if (!cloud_listener_running_) {
        ESP_LOGW(TAG, "Cloud command listener is not running");
        return;
    }
    
    ESP_LOGI(TAG, "Stopping cloud command listener");
    cloud_listener_running_ = false;
    
    // 等待监听线程结束
    if (cloud_listener_thread_.joinable()) {
        cloud_listener_thread_.join();
    }
    
    ESP_LOGI(TAG, "Cloud command listener stopped");
}

void CloudMessageHandler::UpdateConfig(const std::string& api_url, const std::string& api_key, int interval_seconds) {
    // 停止当前监听服务
    StopCloudListener();
    
    // 用新配置重新启动
    StartCloudListener(api_url, api_key, interval_seconds);
}

std::string CloudMessageHandler::GetStatusInfo() const {
    std::string status = "{";
    status += "\"running\": " + std::string(cloud_listener_running_ ? "true" : "false") + ",";
    status += "\"api_url\": \"" + cloud_api_url_ + "\",";
    status += "\"interval\": " + std::to_string(polling_interval_seconds_) + ",";
    
    std::lock_guard<std::mutex> lock(queue_mutex_);
    status += "\"queue_size\": " + std::to_string(message_queue_.size());
    status += "}";
    
    return status;
}

void CloudMessageHandler::CloudListenerLoop() {
    ESP_LOGI(TAG, "Cloud command listener loop started");
    
    while (cloud_listener_running_) {
        try {
            std::string response;
            if (FetchCloudCommand(response)) {
                if (!response.empty()) {
                    ESP_LOGD(TAG, "Received cloud response: %s", response.c_str());
                    ProcessCloudCommand(response);
                }
            }
            
        } catch (const std::exception& e) {
            ESP_LOGE(TAG, "Error in cloud command listener: %s", e.what());
        }
        
        // 等待下次轮询，每秒检查一次是否需要停止
        for (int i = 0; i < polling_interval_seconds_ && cloud_listener_running_; ++i) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    }
    
    ESP_LOGI(TAG, "Cloud command listener loop ended");
}

bool CloudMessageHandler::FetchCloudCommand(std::string& response) {
    // 构建请求URL
    std::string full_url = cloud_api_url_;
    bool has_params = (full_url.find('?') != std::string::npos);
    
    if (!cloud_api_key_.empty()) {
        full_url += has_params ? "&" : "?";
        full_url += "key=" + cloud_api_key_;
        has_params = true;
    }
    
    // 添加时间戳避免缓存
    auto now = std::chrono::system_clock::now();
    auto timestamp = std::chrono::duration_cast<std::chrono::seconds>(now.time_since_epoch()).count();
    full_url += has_params ? "&" : "?";
    full_url += "t=" + std::to_string(timestamp);
    
    ESP_LOGD(TAG, "Polling cloud API: %s", full_url.c_str());
    
    // 创建HTTP客户端
    auto& board = Board::GetInstance();
    auto http = board.CreateHttp();
    
    // 设置请求头
    http->SetHeader("User-Agent", "ESP32-CloudHandler/1.0");
    http->SetHeader("Accept", "application/json");
    if (!cloud_api_key_.empty()) {
        http->SetHeader("Authorization", "Bearer " + cloud_api_key_);
    }
    
    // 发送GET请求
    if (http->Open("GET", full_url)) {
        int status_code = http->GetStatusCode();
        if (status_code == 200) {
            response = http->ReadAll();
            http->Close();
            return true;
        } else {
            ESP_LOGW(TAG, "Cloud API returned status code: %d", status_code);
        }
        http->Close();
    } else {
        ESP_LOGW(TAG, "Failed to connect to cloud API: %s", full_url.c_str());
    }
    
    return false;
}

void CloudMessageHandler::ProcessCloudCommand(const std::string& response) {
    // 解析JSON响应
    cJSON* json = cJSON_Parse(response.c_str());
    if (!json) {
        ESP_LOGE(TAG, "Failed to parse cloud command JSON: %s", response.c_str());
        return;
    }
    
    std::string command_text = "";
    std::string message_to_send = "";
    bool has_command = false;
    bool should_process = false;
    
    // 检查响应格式，支持多种格式
    cJSON* data = cJSON_GetObjectItem(json, "data");
    if (cJSON_IsObject(data)) {
        // 格式1: {"data": {"command": "xxx", "message": "xxx", "new": true}}
        cJSON* command = cJSON_GetObjectItem(data, "command");
        cJSON* message = cJSON_GetObjectItem(data, "message");
        cJSON* is_new = cJSON_GetObjectItem(data, "new");
        
        if (cJSON_IsString(command)) {
            command_text = command->valuestring;
            has_command = true;
        }
        if (cJSON_IsString(message)) {
            message_to_send = message->valuestring;
        }
        
        // 检查是否为新指令
        if (cJSON_IsBool(is_new)) {
            should_process = (is_new->valueint == 1);
        } else {
            should_process = true; // 默认处理
        }
    } else {
        // 格式2: {"command": "xxx", "message": "xxx", "new": true}
        cJSON* command = cJSON_GetObjectItem(json, "command");
        cJSON* message = cJSON_GetObjectItem(json, "message");
        cJSON* text = cJSON_GetObjectItem(json, "text");
        cJSON* is_new = cJSON_GetObjectItem(json, "new");
        
        if (cJSON_IsString(command)) {
            command_text = command->valuestring;
            has_command = true;
        }
        if (cJSON_IsString(message)) {
            message_to_send = message->valuestring;
        } else if (cJSON_IsString(text)) {
            message_to_send = text->valuestring;
        }
        
        // 检查是否为新指令
        if (cJSON_IsBool(is_new)) {
            should_process = (is_new->valueint == 1);
        } else {
            should_process = true; // 默认处理
        }
    }
    
    cJSON_Delete(json);
    
    if (!should_process) {
        ESP_LOGD(TAG, "Skipping old command");
        return;
    }
    
    if (!has_command && message_to_send.empty()) {
        ESP_LOGD(TAG, "No valid command or message found in cloud response");
        return;
    }
    
    // 记录接收到的指令
    if (has_command) {
        ESP_LOGI(TAG, "Received new cloud command: %s", command_text.c_str());
    }
    if (!message_to_send.empty()) {
        ESP_LOGI(TAG, "Processing cloud message: %s", message_to_send.c_str());
    }
    
    // 处理特定指令
    if (has_command) {
        ProcessSpecificCommand(command_text, message_to_send);
    }
}

void CloudMessageHandler::ProcessSpecificCommand(const std::string& command, const std::string& message) {
    if (command == "message") {
        // 默认普通优先级
        HandleMessageCommand(message, kPriorityNormal, kInterruptGraceful);
    } else if (command == "urgent_message") {
        // 紧急消息，强制打断
        HandleMessageCommand(message, kPriorityUrgent, kInterruptForce);
    } else if (command == "high_priority_message") {
        // 高优先级消息，立即打断
        HandleMessageCommand(message, kPriorityHigh, kInterruptImmediate);
    } else if (command == "low_priority_message") {
        // 低优先级消息，不打断
        HandleMessageCommand(message, kPriorityLow, kInterruptNone);
    } else if (command == "play_music" && !message.empty()) {
        // 自动播放音乐
        auto& board = Board::GetInstance();
        auto music = board.GetMusic();
        if (music) {
            ESP_LOGI(TAG, "Auto-playing music from cloud command: %s", message.c_str());
            music->Download(message);
        }
    } else if (command == "volume_control") {
        // 音量控制指令
        ESP_LOGI(TAG, "Processing volume control command: %s", message.c_str());
        // 这里可以添加音量控制逻辑
    } else if (command == "device_status") {
        // 设备状态查询指令
        ESP_LOGI(TAG, "Processing device status command");
        // 可以主动发送设备状态信息
    } else {
        ESP_LOGW(TAG, "Unknown command type: %s", command.c_str());
    }
}

void CloudMessageHandler::HandleMessageCommand(const std::string& message, 
                                              CloudMessagePriority priority,
                                              InterruptStrategy strategy) {
    if (message.empty()) {
        ESP_LOGW(TAG, "Empty message content");
        return;
    }
    
    ESP_LOGI(TAG, "Handling message command: %s (priority: %d, strategy: %d)", 
             message.c_str(), priority, strategy);
    
    try {
        auto& app = Application::GetInstance();
        
        // 检查当前设备状态
        auto device_state = app.GetDeviceState();
        
        // 根据优先级和策略决定是否打断
        if (!ShouldInterrupt(priority, strategy)) {
            ESP_LOGI(TAG, "Message queued due to low priority or no-interrupt strategy");
            // 这里可以实现消息队列，暂时直接返回
            return;
        }
        
        if (device_state == kDeviceStateListening) {
            // 如果在listening状态，直接在当前会话中发送消息
            ESP_LOGI(TAG, "Device in listening state, sending message directly in current session");

            // 1. 先模拟显示用户输入（类似stt消息的效果）
            ESP_LOGI(TAG, ">> %s", message.c_str());
            app.Schedule([message]() {
                auto display = Board::GetInstance().GetDisplay();
                display->SetChatMessage("user", message.c_str());
            });

            // 2. 然后通过protocol发送文本消息到服务器
            app.Schedule([message]() {
                // 构造文本消息JSON格式，模拟STT结果
                std::string json_message = "{\"type\":\"stt\",\"text\":\"" + message + "\",\"final\":true}";
                Application::GetInstance().SendMcpMessage(json_message);
            });
        } else if (device_state == kDeviceStateSpeaking) {
            // 如果在speaking状态，根据优先级决定打断策略
            ESP_LOGI(TAG, "Device in speaking state, executing interrupt strategy: %d", strategy);
            
            // 执行打断操作
            ExecuteInterrupt(strategy);
            
            // 等待状态切换完成（打断后会变成listening状态）
            int wait_time = (strategy == kInterruptForce) ? 50 : 100;
            std::this_thread::sleep_for(std::chrono::milliseconds(wait_time));
            
            // 模拟显示用户输入
            ESP_LOGI(TAG, ">> %s", message.c_str());
            app.Schedule([message]() {
                auto display = Board::GetInstance().GetDisplay();
                display->SetChatMessage("user", message.c_str());
            });
            
            // 直接发送消息，不要调用WakeWordInvoke（会关闭会话）
            app.Schedule([message]() {
                // 构造文本消息JSON格式，模拟STT结果
                std::string json_message = "{\"type\":\"stt\",\"text\":\"" + message + "\",\"final\":true}";
                Application::GetInstance().SendMcpMessage(json_message);
            });
        } else {
            // 如果不在listening/speaking状态，只调用WakeWordInvoke即可
            // WakeWordInvoke会自动建立连接并发送唤醒词消息
            ESP_LOGI(TAG, "Device in idle state, invoking wake word with message");
            
            // 1. 模拟显示用户输入
            ESP_LOGI(TAG, ">> %s", message.c_str());
            app.Schedule([message]() {
                auto display = Board::GetInstance().GetDisplay();
                display->SetChatMessage("user", message.c_str());
            });
            
            // 2. 调用WakeWordInvoke，它会自动处理连接和消息发送
            app.WakeWordInvoke(message);
        }
        
        ESP_LOGI(TAG, "Successfully triggered cloud message flow");
        
    } catch (const std::exception& e) {
        ESP_LOGE(TAG, "Failed to handle message command: %s", e.what());
    }
}

CloudMessagePriority CloudMessageHandler::ParsePriority(const std::string& priority_str) {
    if (priority_str == "urgent") return kPriorityUrgent;
    if (priority_str == "high") return kPriorityHigh;
    if (priority_str == "normal") return kPriorityNormal;
    if (priority_str == "low") return kPriorityLow;
    return kPriorityNormal; // 默认普通优先级
}

InterruptStrategy CloudMessageHandler::DetermineInterruptStrategy(CloudMessagePriority priority) {
    switch (priority) {
        case kPriorityUrgent:
            return kInterruptForce;
        case kPriorityHigh:
            return kInterruptImmediate;
        case kPriorityNormal:
            return kInterruptGraceful;
        case kPriorityLow:
            return kInterruptNone;
        default:
            return kInterruptGraceful;
    }
}

bool CloudMessageHandler::ShouldInterrupt(CloudMessagePriority priority, InterruptStrategy strategy) {
    auto& app = Application::GetInstance();
    auto device_state = app.GetDeviceState();
    
    // 如果设备空闲，总是可以处理
    if (device_state == kDeviceStateIdle) {
        return true;
    }
    
    // 如果设备在listening状态，根据优先级决定
    if (device_state == kDeviceStateListening) {
        return priority >= kPriorityNormal;
    }
    
    // 如果设备在speaking状态，根据打断策略决定
    if (device_state == kDeviceStateSpeaking) {
        switch (strategy) {
            case kInterruptNone:
                return false;
            case kInterruptGraceful:
                return priority >= kPriorityNormal;
            case kInterruptImmediate:
                return priority >= kPriorityHigh;
            case kInterruptForce:
                return priority >= kPriorityUrgent;
            default:
                return false;
        }
    }
    
    return false;
}

void CloudMessageHandler::ExecuteInterrupt(InterruptStrategy strategy) {
    auto& app = Application::GetInstance();
    
    switch (strategy) {
        case kInterruptNone:
            ESP_LOGI(TAG, "No interrupt - waiting for current operation to complete");
            break;
            
        case kInterruptGraceful:
            ESP_LOGI(TAG, "Graceful interrupt - aborting with wake word reason");
            app.AbortSpeaking(kAbortReasonWakeWordDetected);
            break;
            
        case kInterruptImmediate:
            ESP_LOGI(TAG, "Immediate interrupt - aborting current operation");
            app.AbortSpeaking(kAbortReasonWakeWordDetected);
            break;
            
        case kInterruptForce:
            ESP_LOGI(TAG, "Force interrupt - immediately stopping all operations");
            app.AbortSpeaking(kAbortReasonWakeWordDetected);
            // 可以添加更强制的打断逻辑，比如直接关闭音频通道
            break;
            
        default:
            ESP_LOGW(TAG, "Unknown interrupt strategy: %d", strategy);
            break;
    }
} 
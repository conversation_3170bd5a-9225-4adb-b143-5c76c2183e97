<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESP32 云端指令服务器</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #f0f2f5; padding: 20px; }
        .container { max-width: 1000px; margin: 0 auto; background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: #4a90e2; color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { padding: 20px; }
        .section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .section h2 { color: #333; margin-bottom: 15px; border-bottom: 2px solid #4a90e2; padding-bottom: 8px; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group select, .form-group textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { background: #4a90e2; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-right: 10px; }
        .btn:hover { background: #357abd; }
        .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .status-card { background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; border: 1px solid #dee2e6; }
        .command-list { max-height: 300px; overflow-y: auto; border: 1px solid #ddd; border-radius: 5px; }
        .command-item { padding: 10px; border-bottom: 1px solid #eee; }
        .message { padding: 10px; border-radius: 5px; margin: 10px 0; display: none; }
        .message.success { background: #d4edda; color: #155724; }
        .message.error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 ESP32 云端指令服务器</h1>
            <p>管理和发送指令到您的ESP32设备</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>📊 服务器状态</h2>
                <div class="status-grid">
                    <div class="status-card">
                        <h3>队列指令</h3>
                        <div id="queue-count">-</div>
                    </div>
                    <div class="status-card">
                        <h3>历史记录</h3>
                        <div id="history-count">-</div>
                    </div>
                </div>
                <button class="btn" onclick="refreshStatus()">🔄 刷新状态</button>
                <button class="btn" onclick="clearQueue()">🗑️ 清空队列</button>
            </div>
            
            <div class="section">
                <h2>⚡ 快速指令</h2>
                <button class="btn" onclick="quickCommand('play_music', '青花瓷')">🎵 播放音乐</button>
                <button class="btn" onclick="quickCommand('volume_control', '80')">🔊 音量80%</button>
                <button class="btn" onclick="quickCommand('device_status', '')">📱 查询状态</button>
                <button class="btn" onclick="quickCommand('message', '你好ESP32')">💬 发送消息</button>
            </div>
            
            <div class="section">
                <h2>🛠️ 自定义指令</h2>
                <form id="command-form">
                    <div class="form-group">
                        <label for="command-type">指令类型:</label>
                        <select id="command-type" required>
                            <option value="">选择指令类型</option>
                            <option value="play_music">播放音乐</option>
                            <option value="volume_control">音量控制</option>
                            <option value="device_status">设备状态</option>
                            <option value="message">消息</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="priority">优先级:</label>
                        <select id="priority">
                            <option value="low">🟢 低优先级 (不打断)</option>
                            <option value="normal" selected>🟡 普通优先级 (优雅打断)</option>
                            <option value="high">🟠 高优先级 (立即打断)</option>
                            <option value="urgent">🔴 紧急优先级 (强制打断)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="message">消息内容:</label>
                        <textarea id="message" rows="3" placeholder="输入要发送的消息..." required></textarea>
                    </div>
                    <button type="submit" class="btn">📤 发送指令</button>
                </form>
                <div id="message-display" class="message"></div>
            </div>
            
            <div class="section">
                <h2>📋 当前队列</h2>
                <div class="command-list" id="command-queue">
                    <div class="command-item">队列为空</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            refreshStatus();
            refreshCommands();
            setInterval(() => { refreshStatus(); refreshCommands(); }, 5000);
            
            document.getElementById('command-form').addEventListener('submit', function(e) {
                e.preventDefault();
                addCommand();
            });
        });
        
        async function refreshStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                document.getElementById('queue-count').textContent = data.queue_count || 0;
                document.getElementById('history-count').textContent = data.history_count || 0;
            } catch (error) {
                console.error('刷新状态失败:', error);
            }
        }
        
        async function refreshCommands() {
            try {
                const response = await fetch('/api/commands/list');
                const data = await response.json();
                if (data.success) {
                    updateCommandList(data.queue);
                }
            } catch (error) {
                console.error('刷新指令列表失败:', error);
            }
        }
        
        function updateCommandList(commands) {
            const container = document.getElementById('command-queue');
            if (commands.length === 0) {
                container.innerHTML = '<div class="command-item">队列为空</div>';
                return;
            }
            
            container.innerHTML = commands.map(cmd => {
                const priorityIcon = {
                    'low': '🟢',
                    'normal': '🟡', 
                    'high': '🟠',
                    'urgent': '🔴'
                }[cmd.priority] || '🟡';
                
                return `
                <div class="command-item">
                    <strong>${cmd.command}</strong> ${priorityIcon}: ${cmd.message}
                    <br><small>${cmd.timestamp} ${cmd.fetched ? '(已获取)' : '(待获取)'}</small>
                </div>
                `;
            }).join('');
        }
        
        async function addCommand() {
            const commandType = document.getElementById('command-type').value;
            const message = document.getElementById('message').value;
            const priority = document.getElementById('priority').value;
            
            try {
                const response = await fetch('/api/commands', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({command: commandType, message: message, priority: priority})
                });
                
                const data = await response.json();
                if (data.success) {
                    showMessage('指令添加成功！', 'success');
                    document.getElementById('command-form').reset();
                    refreshCommands();
                } else {
                    showMessage('添加失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('添加指令失败: ' + error.message, 'error');
            }
        }
        
        async function quickCommand(type, message) {
            try {
                const response = await fetch('/api/commands/quick', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({type: type, message: message})
                });
                
                const data = await response.json();
                if (data.success) {
                    showMessage('快速指令发送成功！', 'success');
                    refreshCommands();
                } else {
                    showMessage('发送失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('发送快速指令失败: ' + error.message, 'error');
            }
        }
        
        async function clearQueue() {
            if (!confirm('确定要清空所有待处理指令吗？')) return;
            
            try {
                const response = await fetch('/api/commands/clear', { method: 'POST' });
                const data = await response.json();
                if (data.success) {
                    showMessage('队列已清空', 'success');
                    refreshCommands();
                } else {
                    showMessage('清空失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('清空队列失败: ' + error.message, 'error');
            }
        }
        
        function showMessage(text, type) {
            const messageDiv = document.getElementById('message-display');
            messageDiv.textContent = text;
            messageDiv.className = `message ${type}`;
            messageDiv.style.display = 'block';
            setTimeout(() => messageDiv.style.display = 'none', 3000);
        }
    </script>
</body>
</html> 
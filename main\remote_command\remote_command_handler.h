#ifndef REMOTE_COMMAND_HANDLER_H
#define REMOTE_COMMAND_HANDLER_H

#include <string>
#include <functional>
#include <map>
#include <memory>
#include "remote_command_types.h"
#include "cloud_message_handler.h"

/**
 * 远程指令处理器
 * 统一管理远程指令的处理和分发
 */
class RemoteCommandHandler {
public:
    // 获取单例实例
    static RemoteCommandHandler& GetInstance() {
        static RemoteCommandHandler instance;
        return instance;
    }
    
    // 删除拷贝构造函数和赋值运算符
    RemoteCommandHandler(const RemoteCommandHandler&) = delete;
    RemoteCommandHandler& operator=(const RemoteCommandHandler&) = delete;
    
    // 初始化和清理
    void Initialize();
    void Shutdown();
    bool IsInitialized() const { return initialized_; }
    
    // 云端监听服务管理
    void StartCloudService(const std::string& api_url, 
                          const std::string& api_key = "", 
                          int interval_seconds = RemoteCommandConstants::kDefaultPollingInterval);
    void StopCloudService();
    void UpdateCloudConfig(const CloudApiConfig& config);
    bool IsCloudServiceRunning() const;
    
    // 指令处理器注册
    using CommandProcessor = std::function<bool(const RemoteCommand&)>;
    void RegisterCommandProcessor(RemoteCommandType type, CommandProcessor processor);
    void RegisterCommandProcessor(const std::string& command_name, CommandProcessor processor);
    void UnregisterCommandProcessor(RemoteCommandType type);
    void UnregisterCommandProcessor(const std::string& command_name);
    
    // 手动处理指令
    bool ProcessCommand(const RemoteCommand& command);
    bool ProcessCommand(const std::string& command_json);
    
    // 状态查询
    std::string GetStatusInfo() const;
    RemoteCommandStatus GetStatus() const;
    CloudApiConfig GetCloudConfig() const;
    
    // 统计信息
    struct Statistics {
        int total_commands_processed;
        int successful_commands;
        int failed_commands;
        int commands_by_type[10]; // 按类型统计
        std::string last_command_time;
        
        Statistics() : total_commands_processed(0), successful_commands(0), failed_commands(0) {
            for (int i = 0; i < 10; i++) commands_by_type[i] = 0;
        }
    };
    
    Statistics GetStatistics() const { return statistics_; }
    void ResetStatistics();
    
    // 事件回调
    using EventCallback = std::function<void(const std::string& event, const RemoteCommand& command)>;
    void SetEventCallback(EventCallback callback) { event_callback_ = callback; }

private:
    RemoteCommandHandler();
    ~RemoteCommandHandler();
    
    // 内部状态
    bool initialized_;
    Statistics statistics_;
    
    // 指令处理器映射
    std::map<RemoteCommandType, CommandProcessor> type_processors_;
    std::map<std::string, CommandProcessor> name_processors_;
    
    // 云端消息处理器
    std::unique_ptr<CloudMessageHandler> cloud_handler_;
    
    // 事件回调
    EventCallback event_callback_;
    
    // 内部方法
    void OnCloudMessage(const RemoteCommand& command);
    bool ExecuteCommandProcessor(const RemoteCommand& command);
    void UpdateStatistics(const RemoteCommand& command, bool success);
    void TriggerEvent(const std::string& event, const RemoteCommand& command);
    
    // 默认指令处理器
    void RegisterDefaultProcessors();
    bool ProcessMessageCommand(const RemoteCommand& command);
    bool ProcessMusicCommand(const RemoteCommand& command);
    bool ProcessVolumeCommand(const RemoteCommand& command);
    bool ProcessDeviceStatusCommand(const RemoteCommand& command);
    
    // 设备状态和打断处理
    bool ShouldInterrupt(const RemoteCommand& command);
    void ExecuteInterrupt(const RemoteCommand& command);
    InterruptStrategy DetermineInterruptStrategy(const RemoteCommand& command);
};

#endif // REMOTE_COMMAND_HANDLER_H

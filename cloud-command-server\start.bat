@echo off
chcp 65001 >nul
echo ================================
echo 🚀 启动ESP32云端指令服务器
echo ================================

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.7+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查requirements.txt是否存在
if not exist requirements.txt (
    echo ❌ 错误: 未找到requirements.txt文件
    pause
    exit /b 1
)

REM 安装依赖
echo 📦 正在安装依赖包...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败，请检查网络连接
    pause
    exit /b 1
)

REM 启动服务器
echo.
echo ✅ 依赖安装完成，正在启动服务器...
echo.
python app.py

pause
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP32云端指令服务器API测试脚本
用于测试服务器的各个接口功能
"""

import requests
import json
import time

# 服务器配置
BASE_URL = "http://localhost:5000"
API_KEY = "esp32_secret_key_2024"

def test_server_status():
    """测试服务器状态"""
    print("🔍 测试服务器状态...")
    try:
        response = requests.get(f"{BASE_URL}/api/status")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 服务器状态: {data.get('status')}")
            print(f"📊 队列指令数: {data.get('queue_count')}")
            print(f"📜 历史记录数: {data.get('history_count')}")
            return True
        else:
            print(f"❌ 状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def test_add_command():
    """测试添加指令"""
    print("\n📤 测试添加自定义指令...")
    try:
        data = {
            "command": "play_music",
            "message": "测试歌曲《青花瓷》",
            "auto_clear": True
        }
        response = requests.post(
            f"{BASE_URL}/api/commands",
            headers={"Content-Type": "application/json"},
            json=data
        )
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 指令添加成功: {result.get('message')}")
            return True
        else:
            print(f"❌ 添加失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 添加失败: {e}")
        return False

def test_quick_command():
    """测试快速指令"""
    print("\n⚡ 测试快速指令...")
    try:
        data = {
            "type": "music",
            "message": "快速测试歌曲"
        }
        response = requests.post(
            f"{BASE_URL}/api/commands/quick",
            headers={"Content-Type": "application/json"},
            json=data
        )
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 快速指令发送成功: {result.get('message')}")
            return True
        else:
            print(f"❌ 发送失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 发送失败: {e}")
        return False

def test_esp32_get_commands():
    """测试ESP32获取指令接口"""
    print("\n🤖 测试ESP32获取指令...")
    try:
        params = {"key": API_KEY}
        response = requests.get(f"{BASE_URL}/api/commands", params=params)
        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 200:
                command_data = data.get("data", {})
                if command_data.get("new"):
                    print(f"✅ 获取到新指令:")
                    print(f"   指令类型: {command_data.get('command')}")
                    print(f"   消息内容: {command_data.get('message')}")
                    print(f"   时间戳: {command_data.get('timestamp')}")
                else:
                    print("📭 暂无新指令")
                return True
            else:
                print(f"❌ API返回错误码: {data.get('code')}")
                return False
        else:
            print(f"❌ HTTP状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取失败: {e}")
        return False

def test_list_commands():
    """测试获取指令列表"""
    print("\n📋 测试获取指令列表...")
    try:
        response = requests.get(f"{BASE_URL}/api/commands/list")
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                queue = data.get("queue", [])
                history = data.get("history", [])
                print(f"✅ 当前队列指令数: {len(queue)}")
                print(f"✅ 历史记录数: {len(history)}")
                
                if queue:
                    print("📋 队列中的指令:")
                    for cmd in queue[:3]:  # 只显示前3个
                        print(f"   - {cmd.get('command')}: {cmd.get('message')}")
                
                return True
            else:
                print(f"❌ 获取失败: {data.get('message')}")
                return False
        else:
            print(f"❌ HTTP状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取失败: {e}")
        return False

def test_invalid_api_key():
    """测试无效API密钥"""
    print("\n🔐 测试无效API密钥...")
    try:
        params = {"key": "invalid_key"}
        response = requests.get(f"{BASE_URL}/api/commands", params=params)
        if response.status_code == 401:
            print("✅ API密钥验证正常工作")
            return True
        else:
            print(f"❌ 期望状态码401，实际: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("🧪 ESP32云端指令服务器API测试")
    print("=" * 50)
    
    # 测试项目列表
    tests = [
        ("服务器状态", test_server_status),
        ("添加指令", test_add_command),
        ("快速指令", test_quick_command),
        ("ESP32获取指令", test_esp32_get_commands),
        ("指令列表", test_list_commands),
        ("API密钥验证", test_invalid_api_key),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            time.sleep(1)  # 每个测试间隔1秒
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 测试完成: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！服务器工作正常。")
    else:
        print("⚠️  有部分测试失败，请检查服务器配置和状态。")
    
    print("\n💡 提示:")
    print(f"   - 确保服务器正在 {BASE_URL} 上运行")
    print(f"   - 确认API密钥为: {API_KEY}")
    print("   - 可以访问 http://localhost:5000 查看Web界面")

if __name__ == "__main__":
    main() 
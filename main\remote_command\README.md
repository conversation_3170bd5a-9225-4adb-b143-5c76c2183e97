# 远程指令系统

这是一个封装完整的ESP32远程指令处理系统，支持通过云端API接收和处理远程指令。

## 功能特性

- **统一的指令处理接口**: 提供统一的远程指令处理和分发机制
- **云端轮询服务**: 自动从云端API获取远程指令
- **优先级系统**: 支持4级优先级（低、普通、高、紧急）
- **智能打断**: 根据设备状态和指令优先级智能处理打断
- **可扩展架构**: 支持自定义指令处理器注册
- **统计监控**: 提供详细的处理统计和状态监控
- **事件回调**: 支持指令处理事件的回调通知

## 文件结构

```
remote_command/
├── remote_command_types.h      # 类型定义和枚举
├── cloud_message_handler.h     # 云端消息处理器头文件
├── cloud_message_handler.cc    # 云端消息处理器实现
├── remote_command_handler.h    # 远程指令处理器头文件
├── remote_command_handler.cc   # 远程指令处理器实现
├── remote_command_integration.h # MCP集成示例头文件
└── README.md                   # 本文档
```

## 快速开始

### 1. 初始化系统

```cpp
#include "remote_command/remote_command_handler.h"

void setup() {
    // 初始化远程指令处理器
    auto& handler = RemoteCommandHandler::GetInstance();
    handler.Initialize();
    
    // 启动云端服务
    handler.StartCloudService(
        "http://192.168.1.100:5001/api/commands",  // API URL
        "esp32_secret_key_2024",                   // API密钥
        10                                         // 轮询间隔（秒）
    );
}
```

### 2. 注册自定义指令处理器

```cpp
// 按指令类型注册
handler.RegisterCommandProcessor(kCommandPlayMusic, [](const RemoteCommand& cmd) {
    ESP_LOGI("MUSIC", "Playing: %s", cmd.message.c_str());
    // 实现音乐播放逻辑
    return true;
});

// 按指令名称注册
handler.RegisterCommandProcessor("custom_command", [](const RemoteCommand& cmd) {
    ESP_LOGI("CUSTOM", "Processing: %s", cmd.message.c_str());
    // 实现自定义逻辑
    return true;
});
```

### 3. 设置事件回调

```cpp
handler.SetEventCallback([](const std::string& event, const RemoteCommand& command) {
    ESP_LOGI("EVENT", "Event: %s, Command: %s", event.c_str(), command.command.c_str());
});
```

### 4. 手动处理指令

```cpp
RemoteCommand command;
command.command = "message";
command.message = "Hello ESP32";
command.priority = kPriorityNormal;

bool success = handler.ProcessCommand(command);
```

## 与MCP服务器集成

### 在MCP服务器中添加远程指令工具

```cpp
// 在mcp_server.cc的AddCommonTools()方法中添加
void McpServer::AddCommonTools() {
    // ... 现有代码 ...
    
    // 添加远程指令配置工具
    AddTool("self.remote_command.configure",
        "配置远程指令监听服务参数",
        PropertyList({
            Property("api_url", kPropertyTypeString),
            Property("api_key", kPropertyTypeString, ""),
            Property("interval_seconds", kPropertyTypeInteger, 10, 5, 300)
        }),
        [](const PropertyList& properties) -> ReturnValue {
            auto& handler = RemoteCommandHandler::GetInstance();
            CloudApiConfig config;
            config.api_url = properties["api_url"].value<std::string>();
            config.api_key = properties["api_key"].value<std::string>();
            config.polling_interval_seconds = properties["interval_seconds"].value<int>();
            
            handler.UpdateCloudConfig(config);
            return "{\"success\": true, \"message\": \"远程指令服务配置已更新\"}";
        });
    
    // 添加状态查询工具
    AddTool("self.remote_command.status",
        "查看远程指令服务状态",
        PropertyList(),
        [](const PropertyList& properties) -> ReturnValue {
            auto& handler = RemoteCommandHandler::GetInstance();
            return handler.GetStatusInfo();
        });
}
```

## 支持的指令类型

| 指令类型 | 描述 | 优先级 | 打断策略 |
|---------|------|--------|----------|
| `message` | 普通消息 | 普通 | 优雅打断 |
| `urgent_message` | 紧急消息 | 紧急 | 强制打断 |
| `high_priority_message` | 高优先级消息 | 高 | 立即打断 |
| `low_priority_message` | 低优先级消息 | 低 | 不打断 |
| `play_music` | 播放音乐 | 普通 | 优雅打断 |
| `volume_control` | 音量控制 | 普通 | 优雅打断 |
| `device_status` | 设备状态查询 | 低 | 不打断 |

## 云端API格式

### 请求格式
```
GET /api/commands?key=your_api_key&t=timestamp
```

### 响应格式
```json
{
    "code": 200,
    "data": {
        "id": 12345,
        "command": "message",
        "message": "Hello ESP32",
        "priority": "normal",
        "new": true,
        "timestamp": "2024-01-01 12:00:00"
    }
}
```

## 状态监控

```cpp
// 获取详细状态信息
std::string status = handler.GetStatusInfo();

// 获取统计信息
auto stats = handler.GetStatistics();
ESP_LOGI("STATS", "Total: %d, Success: %d, Failed: %d", 
         stats.total_commands_processed, 
         stats.successful_commands, 
         stats.failed_commands);
```

## 注意事项

1. **线程安全**: 所有公共接口都是线程安全的
2. **内存管理**: 系统会自动管理内存，无需手动释放
3. **错误处理**: 所有操作都有完善的错误处理和日志记录
4. **配置持久化**: 配置更改会立即生效，但不会持久化到存储
5. **网络依赖**: 云端服务需要稳定的网络连接

## 故障排除

- **无法连接云端API**: 检查网络连接和API URL
- **指令不被处理**: 检查是否注册了对应的处理器
- **打断不生效**: 检查设备状态和优先级设置
- **内存不足**: 减少轮询频率或队列大小

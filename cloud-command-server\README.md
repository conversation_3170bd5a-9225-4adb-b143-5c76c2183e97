# ESP32 云端指令服务器

这是一个为ESP32设备提供云端指令推送服务的本地服务器。

## 功能特性

- 🚀 **实时指令推送**: ESP32设备自动轮询获取指令
- 📱 **Web管理界面**: 可视化管理指令队列和历史
- ⚡ **快速指令**: 预设常用指令一键发送
- 🛠️ **自定义指令**: 支持任意类型的指令发送
- 📊 **状态监控**: 实时查看服务器和队列状态
- 🔐 **API密钥验证**: 确保只有授权设备可以访问

## 快速开始

### 1. 安装依赖

确保您的系统已安装Python 3.7+，然后运行：

```bash
cd cloud-command-server
pip install -r requirements.txt
```

### 2. 启动服务器

```bash
python app.py
```

服务器启动后，您将看到以下信息：

```
============================================================
🚀 ESP32 云端指令服务器启动中...
============================================================
📡 API 密钥: esp32_secret_key_2024
🌐 ESP32 接口: http://localhost:5000/api/commands?key=esp32_secret_key_2024
📱 Web 管理界面: http://localhost:5000
📚 API 文档: http://localhost:5000/api/status
============================================================
```

### 3. 配置ESP32设备

在您的ESP32代码中，使用以下配置启动云端监听：

```json
{
  "name": "self.cloud_command.configure",
  "arguments": {
    "api_url": "http://你的电脑IP:5000/api/commands",
    "api_key": "esp32_secret_key_2024",
    "interval_seconds": 30
  }
}
```

**注意**: 将 `你的电脑IP` 替换为运行服务器的电脑的实际IP地址。

## API接口说明

### ESP32设备接口

#### GET /api/commands
ESP32设备获取指令的主接口

**参数**:
- `key`: API密钥 (必需)
- `t`: 时间戳 (可选，用于避免缓存)

**响应格式**:
```json
{
  "code": 200,
  "data": {
    "id": 1234567890,
    "command": "play_music",
    "message": "播放《青花瓷》",
    "new": true,
    "timestamp": "2024-01-15 10:30:00"
  }
}
```

### 管理接口

#### POST /api/commands
添加新指令

**请求体**:
```json
{
  "command": "play_music",
  "message": "播放《青花瓷》",
  "auto_clear": true
}
```

#### POST /api/commands/quick
发送快速指令

**请求体**:
```json
{
  "type": "music",
  "message": "青花瓷"
}
```

#### GET /api/commands/list
获取指令队列和历史

#### POST /api/commands/clear
清空指令队列

#### GET /api/status
获取服务器状态

## 支持的指令类型

### 1. 播放音乐 (play_music)
```json
{
  "command": "play_music",
  "message": "青花瓷"
}
```

### 2. 音量控制 (volume_control)
```json
{
  "command": "volume_control", 
  "message": "80"
}
```

### 3. 设备状态 (device_status)
```json
{
  "command": "device_status",
  "message": ""
}
```

### 4. 发送消息 (message)
```json
{
  "command": "message",
  "message": "你好，ESP32！"
}
```

## 使用指南

### Web管理界面

1. 打开浏览器访问 `http://localhost:5000`
2. 在快速指令区域可以一键发送常用指令
3. 在自定义指令区域可以创建任意指令
4. 实时查看指令队列和执行状态

### 指令队列机制

- 指令按FIFO (先进先出) 顺序执行
- ESP32获取指令后，指令自动从队列中移除
- 支持查看最近的执行历史

## 配置说明

### 修改API密钥

编辑 `app.py` 文件中的 `API_KEY` 变量：

```python
API_KEY = "your_custom_api_key"
```

### 修改端口

在 `app.py` 文件末尾修改：

```python
app.run(
    host='0.0.0.0',
    port=8080,  # 修改为您想要的端口
    debug=True,
    threaded=True
)
```

### 允许外网访问

默认配置已经允许外网访问(`host='0.0.0.0'`)，但请确保：

1. 防火墙允许对应端口通过
2. 路由器端口转发配置正确
3. ESP32能够访问服务器IP地址

## 故障排除

### 常见问题

1. **ESP32无法连接服务器**
   - 检查IP地址是否正确
   - 确认防火墙设置
   - 验证网络连通性

2. **API密钥验证失败**
   - 确认ESP32使用的密钥与服务器一致
   - 检查密钥是否包含特殊字符

3. **指令不执行**
   - 查看ESP32日志确认是否收到指令
   - 检查指令格式是否正确
   - 验证ESP32监听服务是否正常运行

### 日志查看

服务器运行时会在控制台显示详细日志，包括：
- 收到的API请求
- 指令发送记录
- 错误信息

## 开发扩展

### 添加新的指令类型

1. 在ESP32代码的 `ProcessSpecificCommand` 方法中添加处理逻辑
2. 在Web界面的指令类型选择框中添加选项
3. 根据需要在快速指令区域添加按钮

### 数据持久化

当前版本使用内存存储，重启后数据会丢失。如需持久化，可以：

1. 使用SQLite数据库
2. 使用JSON文件存储
3. 集成Redis缓存

## 安全注意事项

1. **API密钥**: 使用强密钥并定期更换
2. **网络安全**: 在生产环境中使用HTTPS
3. **访问控制**: 限制服务器的网络访问范围
4. **输入验证**: 对所有输入进行严格验证

## 许可证

MIT License 
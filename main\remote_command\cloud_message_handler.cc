#include "cloud_message_handler.h"
#include "application.h"
#include "board.h"
#include "display.h"
#include "protocol.h"
#include <esp_log.h>
#include <cJSON.h>
#include <chrono>

#define TAG "CloudHandler"

CloudMessageHandler::CloudMessageHandler()
    : cloud_listener_running_(false)
    , current_status_(kStatusIdle)
    , total_requests_(0)
    , successful_requests_(0)
    , failed_requests_(0)
    , low_priority_processor_running_(false) {
    config_.polling_interval_seconds = RemoteCommandConstants::kDefaultPollingInterval;
    config_.max_retry_count = RemoteCommandConstants::kMaxRetryCount;
    config_.timeout_seconds = RemoteCommandConstants::kDefaultTimeout;
}

CloudMessageHandler::~CloudMessageHandler() {
    StopCloudListener();
    StopLowPriorityProcessor();
}

void CloudMessageHandler::StartCloudListener(const std::string& api_url, 
                                           const std::string& api_key, 
                                           int interval_seconds) {
    if (cloud_listener_running_) {
        ESP_LOGW(TAG, "Cloud command listener is already running");
        return;
    }
    
    config_.api_url = api_url;
    config_.api_key = api_key;
    config_.polling_interval_seconds = interval_seconds;
    
    cloud_listener_running_ = true;
    current_status_ = kStatusPolling;
    
    ESP_LOGI(TAG, "Starting cloud command listener: URL=%s, interval=%ds", 
            api_url.c_str(), interval_seconds);
    
    // 启动监听线程
    cloud_listener_thread_ = std::thread(&CloudMessageHandler::CloudListenerLoop, this);

    // 启动低优先级消息处理器
    StartLowPriorityProcessor();
}

void CloudMessageHandler::StopCloudListener() {
    if (!cloud_listener_running_) {
        ESP_LOGW(TAG, "Cloud command listener is not running");
        return;
    }
    
    ESP_LOGI(TAG, "Stopping cloud command listener");
    cloud_listener_running_ = false;
    current_status_ = kStatusIdle;
    
    // 等待监听线程结束
    if (cloud_listener_thread_.joinable()) {
        cloud_listener_thread_.join();
    }

    // 停止低优先级处理器
    StopLowPriorityProcessor();

    ESP_LOGI(TAG, "Cloud command listener stopped");
}

void CloudMessageHandler::UpdateConfig(const std::string& api_url, 
                                      const std::string& api_key, 
                                      int interval_seconds) {
    // 停止当前监听服务
    StopCloudListener();
    
    // 用新配置重新启动
    StartCloudListener(api_url, api_key, interval_seconds);
}

void CloudMessageHandler::UpdateConfig(const CloudApiConfig& config) {
    UpdateConfig(config.api_url, config.api_key, config.polling_interval_seconds);
}

CloudApiConfig CloudMessageHandler::GetConfig() const {
    return config_;
}

std::string CloudMessageHandler::GetStatusInfo() const {
    std::string status = "{";
    status += "\"running\": " + std::string(cloud_listener_running_ ? "true" : "false") + ",";
    status += "\"status\": " + std::to_string(static_cast<int>(current_status_)) + ",";
    status += "\"api_url\": \"" + config_.api_url + "\",";
    status += "\"interval\": " + std::to_string(config_.polling_interval_seconds) + ",";

    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        status += "\"queue_size\": " + std::to_string(message_queue_.size()) + ",";
    }

    {
        std::lock_guard<std::mutex> lock(low_priority_mutex_);
        status += "\"low_priority_queue_size\": " + std::to_string(low_priority_queue_.size()) + ",";
    }

    status += "\"low_priority_processor_running\": " + std::string(low_priority_processor_running_ ? "true" : "false") + ",";
    status += GetStatisticsInfo();
    status += "}";

    return status;
}

void CloudMessageHandler::SetMessageCallback(std::function<void(const RemoteCommand&)> callback) {
    message_callback_ = callback;
}

bool CloudMessageHandler::TriggerManualPoll() {
    if (!cloud_listener_running_) {
        ESP_LOGW(TAG, "Cloud listener is not running, cannot trigger manual poll");
        return false;
    }
    
    try {
        std::string response;
        if (FetchCloudCommand(response)) {
            if (!response.empty()) {
                ProcessCloudCommand(response);
                return true;
            }
        }
    } catch (const std::exception& e) {
        ESP_LOGE(TAG, "Error in manual poll: %s", e.what());
    }
    
    return false;
}

void CloudMessageHandler::CloudListenerLoop() {
    ESP_LOGI(TAG, "Cloud command listener loop started");
    
    while (cloud_listener_running_) {
        try {
            current_status_ = kStatusPolling;
            std::string response;
            
            if (FetchCloudCommand(response)) {
                if (!response.empty()) {
                    ESP_LOGD(TAG, "Received cloud response: %s", response.c_str());
                    current_status_ = kStatusProcessing;
                    ProcessCloudCommand(response);
                    UpdateStatistics(true);
                    last_successful_poll_ = std::chrono::steady_clock::now();
                } else {
                    UpdateStatistics(true); // 空响应也算成功
                }
            } else {
                UpdateStatistics(false);
                current_status_ = kStatusError;
            }
            
        } catch (const std::exception& e) {
            ESP_LOGE(TAG, "Error in cloud command listener: %s", e.what());
            UpdateStatistics(false);
            current_status_ = kStatusError;
        }
        
        // 等待下次轮询，每秒检查一次是否需要停止
        current_status_ = kStatusIdle;
        for (int i = 0; i < config_.polling_interval_seconds && cloud_listener_running_; ++i) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    }
    
    current_status_ = kStatusIdle;
    ESP_LOGI(TAG, "Cloud command listener loop ended");
}

std::string CloudMessageHandler::GetStatisticsInfo() const {
    std::string stats = "\"statistics\": {";
    stats += "\"total_requests\": " + std::to_string(total_requests_.load()) + ",";
    stats += "\"successful_requests\": " + std::to_string(successful_requests_.load()) + ",";
    stats += "\"failed_requests\": " + std::to_string(failed_requests_.load());
    
    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - last_successful_poll_);
    stats += ",\"seconds_since_last_success\": " + std::to_string(duration.count());
    stats += "}";
    
    return stats;
}

void CloudMessageHandler::UpdateStatistics(bool success) {
    total_requests_++;
    if (success) {
        successful_requests_++;
    } else {
        failed_requests_++;
    }
}

bool CloudMessageHandler::FetchCloudCommand(std::string& response) {
    // 构建请求URL
    std::string full_url = config_.api_url;
    bool has_params = (full_url.find('?') != std::string::npos);

    if (!config_.api_key.empty()) {
        full_url += has_params ? "&" : "?";
        full_url += "key=" + config_.api_key;
        has_params = true;
    }

    // 添加时间戳避免缓存
    auto now = std::chrono::system_clock::now();
    auto timestamp = std::chrono::duration_cast<std::chrono::seconds>(now.time_since_epoch()).count();
    full_url += has_params ? "&" : "?";
    full_url += "t=" + std::to_string(timestamp);

    ESP_LOGD(TAG, "Polling cloud API: %s", full_url.c_str());

    // 创建HTTP客户端
    auto& board = Board::GetInstance();
    auto http = board.CreateHttp();

    // 设置请求头
    http->SetHeader("User-Agent", "ESP32-CloudHandler/1.0");
    http->SetHeader("Accept", "application/json");
    if (!config_.api_key.empty()) {
        http->SetHeader("Authorization", "Bearer " + config_.api_key);
    }

    // 发送GET请求
    if (http->Open("GET", full_url)) {
        int status_code = http->GetStatusCode();
        if (status_code == 200) {
            response = http->ReadAll();
            http->Close();
            return true;
        } else {
            ESP_LOGW(TAG, "Cloud API returned status code: %d", status_code);
        }
        http->Close();
    } else {
        ESP_LOGW(TAG, "Failed to connect to cloud API: %s", full_url.c_str());
    }

    return false;
}

void CloudMessageHandler::ProcessCloudCommand(const std::string& response) {
    try {
        RemoteCommand command = ParseCommandFromJson(response);
        if (command.is_new && !command.message.empty()) {
            ESP_LOGI(TAG, "Processing new command: %s - %s", command.command.c_str(), command.message.c_str());

            // 添加到队列
            {
                std::lock_guard<std::mutex> lock(queue_mutex_);
                if (message_queue_.size() < RemoteCommandConstants::kMaxQueueSize) {
                    message_queue_.push(command);
                } else {
                    ESP_LOGW(TAG, "Message queue is full, dropping oldest message");
                    message_queue_.pop();
                    message_queue_.push(command);
                }
            }

            // 调用回调处理
            if (message_callback_) {
                message_callback_(command);
            } else {
                // 默认处理
                ProcessSpecificCommand(command);
            }
        }
    } catch (const std::exception& e) {
        ESP_LOGE(TAG, "Error processing cloud command: %s", e.what());
    }
}

RemoteCommand CloudMessageHandler::ParseCommandFromJson(const std::string& json_response) {
    RemoteCommand command;

    cJSON* json = cJSON_Parse(json_response.c_str());
    if (!json) {
        ESP_LOGE(TAG, "Failed to parse JSON: %s", json_response.c_str());
        return command;
    }

    // 检查响应格式，支持多种格式
    cJSON* data = cJSON_GetObjectItem(json, "data");
    cJSON* cmd_obj = data ? data : json;

    // 解析基本字段
    cJSON* cmd_field = cJSON_GetObjectItem(cmd_obj, "command");
    cJSON* msg_field = cJSON_GetObjectItem(cmd_obj, "message");
    cJSON* text_field = cJSON_GetObjectItem(cmd_obj, "text");
    cJSON* new_field = cJSON_GetObjectItem(cmd_obj, "new");
    cJSON* id_field = cJSON_GetObjectItem(cmd_obj, "id");
    cJSON* priority_field = cJSON_GetObjectItem(cmd_obj, "priority");

    if (cJSON_IsString(cmd_field)) {
        command.command = cmd_field->valuestring;
        command.type = ParseCommandType(command.command);
    }

    if (cJSON_IsString(msg_field)) {
        command.message = msg_field->valuestring;
    } else if (cJSON_IsString(text_field)) {
        command.message = text_field->valuestring;
    }

    if (cJSON_IsBool(new_field)) {
        command.is_new = (new_field->valueint == 1);
    } else {
        command.is_new = true; // 默认为新指令
    }

    if (cJSON_IsNumber(id_field)) {
        command.id = id_field->valueint;
    }

    if (cJSON_IsString(priority_field)) {
        command.priority = ParsePriority(priority_field->valuestring);
    }

    cJSON_Delete(json);
    return command;
}

CloudMessagePriority CloudMessageHandler::ParsePriority(const std::string& priority_str) {
    if (priority_str == "urgent") return kPriorityUrgent;
    if (priority_str == "high") return kPriorityHigh;
    if (priority_str == "normal") return kPriorityNormal;
    if (priority_str == "low") return kPriorityLow;
    return kPriorityNormal; // 默认普通优先级
}

RemoteCommandType CloudMessageHandler::ParseCommandType(const std::string& command_str) {
    if (command_str == "urgent_message") return kCommandUrgentMessage;
    if (command_str == "high_priority_message") return kCommandHighPriorityMessage;
    if (command_str == "low_priority_message") return kCommandLowPriorityMessage;
    if (command_str == "play_music") return kCommandPlayMusic;
    if (command_str == "volume_control") return kCommandVolumeControl;
    if (command_str == "device_status") return kCommandDeviceStatus;
    if (command_str == "message") return kCommandMessage;
    return kCommandCustom;
}

void CloudMessageHandler::ProcessSpecificCommand(const RemoteCommand& command) {
    ESP_LOGI(TAG, "Processing specific command: %s - %s", command.command.c_str(), command.message.c_str());

    // 根据指令类型进行基础处理
    if (command.command == "message" ||
        command.command == "urgent_message" ||
        command.command == "high_priority_message" ||
        command.command == "low_priority_message") {
        // 处理消息类指令
        HandleMessageCommand(command);
    } else if (command.command == "play_music") {
        // 处理音乐播放指令
        ESP_LOGI(TAG, "Processing music command: %s", command.message.c_str());
        auto& board = Board::GetInstance();
        auto music = board.GetMusic();
        if (music) {
            music->Download(command.message);
        }
    } else if (command.command == "volume_control") {
        // 处理音量控制指令
        ESP_LOGI(TAG, "Processing volume control: %s", command.message.c_str());
        try {
            int volume = std::stoi(command.message);
            if (volume >= 0 && volume <= 100) {
                auto& board = Board::GetInstance();
                auto codec = board.GetAudioCodec();
                if (codec) {
                    codec->SetOutputVolume(volume);
                }
            }
        } catch (const std::exception& e) {
            ESP_LOGE(TAG, "Invalid volume value: %s", command.message.c_str());
        }
    } else if (command.command == "device_status") {
        // 处理设备状态查询
        ESP_LOGI(TAG, "Processing device status query");
        auto& board = Board::GetInstance();
        std::string status = board.GetDeviceStatusJson();
        ESP_LOGI(TAG, "Device status: %s", status.c_str());
    } else {
        ESP_LOGW(TAG, "Unknown command type: %s", command.command.c_str());
    }
}

void CloudMessageHandler::HandleMessageCommand(const RemoteCommand& command) {
    ESP_LOGI(TAG, "Handling message command: %s", command.message.c_str());

    if (command.message.empty()) {
        ESP_LOGW(TAG, "Empty message content");
        return;
    }

    try {
        auto& app = Application::GetInstance();
        auto device_state = app.GetDeviceState();

        ESP_LOGI(TAG, "Device state: %d, processing message: %s",
                 static_cast<int>(device_state), command.message.c_str());

        // 根据优先级决定打断策略
        bool should_interrupt = ShouldInterrupt(command.priority, DetermineInterruptStrategy(command.priority));

        if (device_state == kDeviceStateListening) {
            // 设备正在监听，直接在当前会话中发送消息
            ESP_LOGI(TAG, "Device listening, sending message in current session");

            // 显示用户消息
            app.Schedule([command]() {
                auto display = Board::GetInstance().GetDisplay();
                if (display) {
                    display->SetChatMessage("user", command.message.c_str());
                }
            });

            // 发送文本消息
            app.SendTextMessage(command.message);

        } else if (device_state == kDeviceStateSpeaking) {
            // 设备正在说话，根据优先级决定是否打断
            if (should_interrupt) {
                ESP_LOGI(TAG, "Device speaking, interrupting with priority: %d", command.priority);
                ExecuteInterrupt(DetermineInterruptStrategy(command.priority));

                // 等待打断完成
                std::this_thread::sleep_for(std::chrono::milliseconds(100));

                // 显示用户消息
                app.Schedule([command]() {
                    auto display = Board::GetInstance().GetDisplay();
                    if (display) {
                        display->SetChatMessage("user", command.message.c_str());
                    }
                });

                // 发送文本消息
                app.SendTextMessage(command.message);
            } else {
                ESP_LOGI(TAG, "Device speaking, message queued (low priority)");
                // 低优先级消息，添加到延迟处理队列
                QueueLowPriorityMessage(command);
            }

        } else {
            // 设备空闲，启动新的对话会话
            ESP_LOGI(TAG, "Device idle, starting new conversation session");

            // 显示用户消息
            app.Schedule([command]() {
                auto display = Board::GetInstance().GetDisplay();
                if (display) {
                    display->SetChatMessage("user", command.message.c_str());
                }
            });

            // 使用WakeWordInvoke启动新会话
            app.WakeWordInvoke(command.message);
        }

    } catch (const std::exception& e) {
        ESP_LOGE(TAG, "Error handling message command: %s", e.what());
    }
}

InterruptStrategy CloudMessageHandler::DetermineInterruptStrategy(CloudMessagePriority priority) {
    switch (priority) {
        case kPriorityUrgent:
            return kInterruptForce;
        case kPriorityHigh:
            return kInterruptImmediate;
        case kPriorityNormal:
            return kInterruptGraceful;
        case kPriorityLow:
            return kInterruptNone;
        default:
            return kInterruptGraceful;
    }
}

bool CloudMessageHandler::ShouldInterrupt(CloudMessagePriority priority, InterruptStrategy strategy) {
    auto& app = Application::GetInstance();
    auto device_state = app.GetDeviceState();

    // 如果设备空闲，总是可以处理
    if (device_state == kDeviceStateIdle) {
        return true;
    }

    // 如果设备在listening状态，根据优先级决定
    if (device_state == kDeviceStateListening) {
        return priority >= kPriorityNormal;
    }

    // 如果设备在speaking状态，根据打断策略决定
    if (device_state == kDeviceStateSpeaking) {
        switch (strategy) {
            case kInterruptNone:
                return false;
            case kInterruptGraceful:
                return priority >= kPriorityNormal;
            case kInterruptImmediate:
                return priority >= kPriorityHigh;
            case kInterruptForce:
                return priority >= kPriorityUrgent;
            default:
                return false;
        }
    }

    return false;
}

void CloudMessageHandler::ExecuteInterrupt(InterruptStrategy strategy) {
    auto& app = Application::GetInstance();

    switch (strategy) {
        case kInterruptNone:
            ESP_LOGI(TAG, "No interrupt - waiting for current operation to complete");
            break;

        case kInterruptGraceful:
            ESP_LOGI(TAG, "Graceful interrupt - aborting with wake word reason");
            app.AbortSpeaking(kAbortReasonWakeWordDetected);
            break;

        case kInterruptImmediate:
            ESP_LOGI(TAG, "Immediate interrupt - aborting current operation");
            app.AbortSpeaking(kAbortReasonWakeWordDetected);
            break;

        case kInterruptForce:
            ESP_LOGI(TAG, "Force interrupt - immediately stopping all operations");
            app.AbortSpeaking(kAbortReasonWakeWordDetected);
            break;

        default:
            ESP_LOGW(TAG, "Unknown interrupt strategy: %d", strategy);
            break;
    }
}

void CloudMessageHandler::HandleError(const std::string& error_message) {
    ESP_LOGE(TAG, "Cloud message handler error: %s", error_message.c_str());
    current_status_ = kStatusError;
}

void CloudMessageHandler::ResetErrorState() {
    ESP_LOGI(TAG, "Resetting error state");
    current_status_ = kStatusIdle;
}

void CloudMessageHandler::QueueLowPriorityMessage(const RemoteCommand& command) {
    std::lock_guard<std::mutex> lock(low_priority_mutex_);

    // 检查队列大小，避免内存溢出
    if (low_priority_queue_.size() >= RemoteCommandConstants::kMaxQueueSize / 2) {
        ESP_LOGW(TAG, "Low priority queue is full, dropping oldest message");
        low_priority_queue_.pop();
    }

    low_priority_queue_.push(command);
    ESP_LOGI(TAG, "Low priority message queued: %s (queue size: %zu)",
             command.message.c_str(), low_priority_queue_.size());
}

void CloudMessageHandler::ProcessLowPriorityQueue() {
    ESP_LOGI(TAG, "Low priority message processor started");

    while (low_priority_processor_running_) {
        try {
            RemoteCommand command;
            bool has_message = false;

            // 检查是否有待处理的低优先级消息
            {
                std::lock_guard<std::mutex> lock(low_priority_mutex_);
                if (!low_priority_queue_.empty()) {
                    command = low_priority_queue_.front();
                    low_priority_queue_.pop();
                    has_message = true;
                }
            }

            if (has_message) {
                // 检查设备状态，只有在空闲或监听状态时才处理
                auto& app = Application::GetInstance();
                auto device_state = app.GetDeviceState();

                if (device_state == kDeviceStateIdle || device_state == kDeviceStateListening) {
                    ESP_LOGI(TAG, "Processing queued low priority message: %s", command.message.c_str());

                    // 处理消息（不再检查优先级，直接处理）
                    if (device_state == kDeviceStateListening) {
                        // 在当前会话中发送
                        app.Schedule([command]() {
                            auto display = Board::GetInstance().GetDisplay();
                            if (display) {
                                display->SetChatMessage("user", command.message.c_str());
                            }
                        });
                        app.SendTextMessage(command.message);
                    } else {
                        // 启动新会话
                        app.Schedule([command]() {
                            auto display = Board::GetInstance().GetDisplay();
                            if (display) {
                                display->SetChatMessage("user", command.message.c_str());
                            }
                        });
                        app.WakeWordInvoke(command.message);
                    }
                } else {
                    // 设备仍在忙碌，重新放回队列
                    std::lock_guard<std::mutex> lock(low_priority_mutex_);
                    low_priority_queue_.push(command);
                    ESP_LOGD(TAG, "Device still busy, re-queuing low priority message");
                }
            }

            // 等待一段时间再检查
            std::this_thread::sleep_for(std::chrono::seconds(2));

        } catch (const std::exception& e) {
            ESP_LOGE(TAG, "Error in low priority processor: %s", e.what());
        }
    }

    ESP_LOGI(TAG, "Low priority message processor stopped");
}

void CloudMessageHandler::StartLowPriorityProcessor() {
    if (low_priority_processor_running_) {
        ESP_LOGW(TAG, "Low priority processor already running");
        return;
    }

    ESP_LOGI(TAG, "Starting low priority message processor");
    low_priority_processor_running_ = true;
    low_priority_processor_thread_ = std::thread(&CloudMessageHandler::ProcessLowPriorityQueue, this);
}

void CloudMessageHandler::StopLowPriorityProcessor() {
    if (!low_priority_processor_running_) {
        return;
    }

    ESP_LOGI(TAG, "Stopping low priority message processor");
    low_priority_processor_running_ = false;

    if (low_priority_processor_thread_.joinable()) {
        low_priority_processor_thread_.join();
    }

    // 清空队列
    std::lock_guard<std::mutex> lock(low_priority_mutex_);
    while (!low_priority_queue_.empty()) {
        low_priority_queue_.pop();
    }

    ESP_LOGI(TAG, "Low priority message processor stopped");
}

#ifndef CLOUD_MESSAGE_HANDLER_H
#define CLOUD_MESSAGE_HANDLER_H

#include <string>
#include <thread>
#include <atomic>
#include <mutex>
#include <queue>
#include <chrono>

// 云端消息优先级
enum CloudMessagePriority {
    kPriorityLow = 0,      // 低优先级：不打断当前操作
    kPriorityNormal = 1,   // 普通优先级：在合适时机处理
    kPriorityHigh = 2,     // 高优先级：立即打断并处理
    kPriorityUrgent = 3    // 紧急优先级：强制打断所有操作
};

// 打断策略
enum InterruptStrategy {
    kInterruptNone = 0,        // 不打断，等待当前操作完成
    kInterruptGraceful = 1,    // 优雅打断，等待合适时机
    kInterruptImmediate = 2,   // 立即打断
    kInterruptForce = 3        // 强制打断，忽略所有状态
};

class CloudMessageHandler {
public:
    static CloudMessageHandler& GetInstance() {
        static CloudMessageHandler instance;
        return instance;
    }
    
    // 删除拷贝构造函数和赋值运算符
    CloudMessageHandler(const CloudMessageHandler&) = delete;
    CloudMessageHandler& operator=(const CloudMessageHandler&) = delete;
    
    // 启动和停止云端监听服务
    void StartCloudListener(const std::string& api_url, const std::string& api_key = "", int interval_seconds = 30);
    void StopCloudListener();
    bool IsCloudListenerRunning() const { return cloud_listener_running_; }
    
    // 配置管理
    void UpdateConfig(const std::string& api_url, const std::string& api_key = "", int interval_seconds = 30);
    
    // 获取状态信息
    std::string GetStatusInfo() const;

private:
    CloudMessageHandler();
    ~CloudMessageHandler();
    
    // 监听线程相关
    std::atomic<bool> cloud_listener_running_;
    std::thread cloud_listener_thread_;
    std::string cloud_api_url_;
    std::string cloud_api_key_;
    int polling_interval_seconds_;
    
    // 消息队列
    std::queue<std::string> message_queue_;
    mutable std::mutex queue_mutex_;
    const int max_queue_size_ = 100;
    
    // 私有方法
    void CloudListenerLoop();
    void ProcessCloudCommand(const std::string& response);
    void ProcessSpecificCommand(const std::string& command, const std::string& message);
    void HandleMessageCommand(const std::string& message, 
                             CloudMessagePriority priority = kPriorityNormal,
                             InterruptStrategy strategy = kInterruptGraceful);
    bool FetchCloudCommand(std::string& response);
    
    // 解析消息优先级
    CloudMessagePriority ParsePriority(const std::string& priority_str);
    
    // 根据优先级确定打断策略
    InterruptStrategy DetermineInterruptStrategy(CloudMessagePriority priority);
    
    // 检查是否应该打断当前操作
    bool ShouldInterrupt(CloudMessagePriority priority, InterruptStrategy strategy);
    
    // 执行打断操作
    void ExecuteInterrupt(InterruptStrategy strategy);
};

#endif // CLOUD_MESSAGE_HANDLER_H 
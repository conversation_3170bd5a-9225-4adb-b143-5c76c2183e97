#ifndef REMOTE_COMMAND_INTEGRATION_H
#define REMOTE_COMMAND_INTEGRATION_H

#include "remote_command_handler.h"
#include "../mcp_server.h"

/**
 * 远程指令与MCP服务器集成工具
 * 提供便捷的集成方法，将远程指令功能添加到现有的MCP服务器中
 */
class RemoteCommandIntegration {
public:
    /**
     * 将远程指令工具添加到MCP服务器
     * @param mcp_server MCP服务器实例
     * @param auto_start 是否自动启动云端服务
     * @param default_api_url 默认API URL
     * @param default_api_key 默认API密钥
     */
    static void AddToMcpServer(McpServer& mcp_server, 
                              bool auto_start = true,
                              const std::string& default_api_url = "http://192.168.18.20:5001/api/commands",
                              const std::string& default_api_key = "esp32_secret_key_2024");
    
    /**
     * 初始化远程指令系统
     * @param auto_start_cloud 是否自动启动云端服务
     */
    static void Initialize(bool auto_start_cloud = true);
    
    /**
     * 注册默认的指令处理器
     * 包括消息、音乐、音量控制等常用指令
     */
    static void RegisterDefaultHandlers();
    
    /**
     * 设置默认的事件回调
     * 提供基本的日志记录和状态更新
     */
    static void SetupDefaultEventHandling();

private:
    // 工具回调函数
    static ReturnValue ConfigureRemoteCommand(const PropertyList& properties);
    static ReturnValue GetRemoteCommandStatus(const PropertyList& properties);
    static ReturnValue TriggerManualPoll(const PropertyList& properties);
    static ReturnValue ResetStatistics(const PropertyList& properties);
    
    // 默认指令处理器
    static bool HandleMusicCommand(const RemoteCommand& command);
    static bool HandleVolumeCommand(const RemoteCommand& command);
    static bool HandleDeviceStatusCommand(const RemoteCommand& command);
    
    // 事件处理
    static void OnRemoteCommandEvent(const std::string& event, const RemoteCommand& command);
    
    // 配置常量
    static constexpr int kDefaultPollingInterval = 3;  // 适合直播场景的快速轮询
    static constexpr const char* kDefaultApiUrl = "http://192.168.18.20:5001/api/commands";
    static constexpr const char* kDefaultApiKey = "esp32_secret_key_2024";
};

/**
 * 便捷的初始化宏
 * 在main.cc中使用，一行代码完成远程指令系统的初始化
 */
#define INIT_REMOTE_COMMAND_SYSTEM() \
    do { \
        RemoteCommandIntegration::Initialize(true); \
        RemoteCommandIntegration::RegisterDefaultHandlers(); \
        RemoteCommandIntegration::SetupDefaultEventHandling(); \
    } while(0)

/**
 * 便捷的MCP集成宏
 * 在mcp_server.cc的AddCommonTools()中使用
 */
#define ADD_REMOTE_COMMAND_TOOLS(mcp_server) \
    RemoteCommandIntegration::AddToMcpServer(mcp_server, true)

#endif // REMOTE_COMMAND_INTEGRATION_H

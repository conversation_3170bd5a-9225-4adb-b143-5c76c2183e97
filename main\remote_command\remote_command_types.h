#ifndef REMOTE_COMMAND_TYPES_H
#define REMOTE_COMMAND_TYPES_H

#include <string>

/**
 * 远程指令类型定义
 * 定义了远程指令系统中使用的各种枚举类型和常量
 */

// 云端消息优先级
enum CloudMessagePriority {
    kPriorityLow = 0,      // 低优先级：不打断当前操作
    kPriorityNormal = 1,   // 普通优先级：在合适时机处理
    kPriorityHigh = 2,     // 高优先级：立即打断并处理
    kPriorityUrgent = 3    // 紧急优先级：强制打断所有操作
};

// 打断策略
enum InterruptStrategy {
    kInterruptNone = 0,        // 不打断，等待当前操作完成
    kInterruptGraceful = 1,    // 优雅打断，等待合适时机
    kInterruptImmediate = 2,   // 立即打断
    kInterruptForce = 3        // 强制打断，忽略所有状态
};

// 远程指令类型
enum RemoteCommandType {
    kCommandMessage = 0,           // 普通消息
    kCommandUrgentMessage = 1,     // 紧急消息
    kCommandHighPriorityMessage = 2, // 高优先级消息
    kCommandLowPriorityMessage = 3,  // 低优先级消息
    kCommandPlayMusic = 4,         // 播放音乐
    kCommandVolumeControl = 5,     // 音量控制
    kCommandDeviceStatus = 6,      // 设备状态查询
    kCommandCustom = 99            // 自定义指令
};

// 远程指令结构体
struct RemoteCommand {
    int id;                        // 指令ID
    RemoteCommandType type;        // 指令类型
    std::string command;           // 指令名称
    std::string message;           // 消息内容
    CloudMessagePriority priority; // 优先级
    bool is_new;                   // 是否为新指令
    std::string timestamp;         // 时间戳
    bool auto_clear;               // 是否自动清除
    
    RemoteCommand() : id(0), type(kCommandMessage), priority(kPriorityNormal), 
                     is_new(false), auto_clear(true) {}
};

// 云端API配置
struct CloudApiConfig {
    std::string api_url;           // API URL
    std::string api_key;           // API密钥
    int polling_interval_seconds;  // 轮询间隔（秒）
    int max_retry_count;           // 最大重试次数
    int timeout_seconds;           // 请求超时时间
    
    CloudApiConfig() : polling_interval_seconds(10), max_retry_count(3), timeout_seconds(30) {}
};

// 远程指令处理状态
enum RemoteCommandStatus {
    kStatusIdle = 0,               // 空闲状态
    kStatusPolling = 1,            // 轮询中
    kStatusProcessing = 2,         // 处理中
    kStatusError = 3               // 错误状态
};

// 常量定义
namespace RemoteCommandConstants {
    const int kDefaultPollingInterval = 10;     // 默认轮询间隔（秒）
    const int kMaxQueueSize = 100;              // 最大队列大小
    const int kMaxRetryCount = 3;               // 最大重试次数
    const int kDefaultTimeout = 30;             // 默认超时时间（秒）
    const char* const kDefaultApiKey = "esp32_secret_key_2024";
}

#endif // REMOTE_COMMAND_TYPES_H

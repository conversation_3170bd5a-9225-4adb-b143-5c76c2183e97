#ifndef REMOTE_COMMAND_TEST_H
#define REMOTE_COMMAND_TEST_H

#include "remote_command_handler.h"
#include "cloud_message_handler.h"

/**
 * 远程指令系统测试工具
 * 用于验证远程对话功能是否正常工作
 */
class RemoteCommandTest {
public:
    /**
     * 运行所有测试
     * @return 测试是否全部通过
     */
    static bool RunAllTests();
    
    /**
     * 测试云端消息处理
     */
    static bool TestCloudMessageProcessing();
    
    /**
     * 测试对话功能
     */
    static bool TestConversationFlow();
    
    /**
     * 测试优先级和打断机制
     */
    static bool TestPriorityAndInterrupt();
    
    /**
     * 测试JSON解析
     */
    static bool TestJsonParsing();
    
    /**
     * 模拟云端API响应
     */
    static std::string CreateMockApiResponse(const std::string& command, 
                                           const std::string& message, 
                                           bool is_new = true);
    
    /**
     * 验证消息是否被正确处理
     */
    static bool VerifyMessageProcessed(const std::string& expected_message);

private:
    static bool test_results_[10];  // 存储各个测试的结果
    static int test_count_;         // 测试计数器
    
    static void LogTestResult(const std::string& test_name, bool passed);
    static void ResetTestResults();
};

/**
 * 便捷的测试宏
 * 在开发调试时使用
 */
#define RUN_REMOTE_COMMAND_TESTS() \
    do { \
        ESP_LOGI("TEST", "Starting remote command tests..."); \
        bool all_passed = RemoteCommandTest::RunAllTests(); \
        ESP_LOGI("TEST", "Remote command tests %s", all_passed ? "PASSED" : "FAILED"); \
    } while(0)

#endif // REMOTE_COMMAND_TEST_H
